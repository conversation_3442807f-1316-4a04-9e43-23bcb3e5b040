# -*- coding: utf-8 -*-
"""
现代化UI样式定义
包含科技感浅色主题、Neumorphism效果、渐变与光效等现代设计元素
"""

class ModernUIStyles:
    """现代化UI样式类"""
    
    # 色彩方案
    COLORS = {
        'primary': '#667eea',      # 科技蓝
        'secondary': '#764ba2',    # 深紫
        'background': '#f8fafc',   # 浅灰白
        'surface': '#ffffff',      # 纯白
        'text_primary': '#1e293b', # 深灰
        'text_secondary': '#64748b', # 中灰
        'success': '#10b981',      # 翠绿
        'warning': '#f59e0b',      # 橙黄
        'error': '#ef4444',        # 红色
        'info': '#3b82f6',         # 蓝色
        'border': '#e2e8f0',       # 边框色
        'shadow': 'rgba(0, 0, 0, 0.1)', # 阴影色
        'glass': 'rgba(255, 255, 255, 0.25)', # 玻璃效果
    }
    
    @staticmethod
    def get_main_window_style():
        """获取主窗口样式"""
        return f"""
        QWidget {{
            background: {ModernUIStyles.COLORS['background']};
            color: {ModernUIStyles.COLORS['text_primary']};
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            font-size: 11pt;
        }}
        
        QMainWindow {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['background']} 0%, #e2e8f0 100%);
        }}
        """
    
    @staticmethod
    def get_modern_card_style():
        """获取现代化卡片样式"""
        return f"""
        QFrame {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-radius: 16px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 4px 6px -1px {ModernUIStyles.COLORS['shadow']}, 
                       0 2px 4px -1px {ModernUIStyles.COLORS['shadow']};
        }}
        
        QGroupBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 12px;
            padding: 15px;
            margin: 8px;
            font-weight: 600;
            font-size: 12pt;
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background: {ModernUIStyles.COLORS['surface']};
            color: {ModernUIStyles.COLORS['primary']};
        }}
        """
    
    @staticmethod
    def get_modern_button_style():
        """获取现代化按钮样式"""
        return f"""
        QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-height: 20px;
            box-shadow: 0 4px 6px -1px {ModernUIStyles.COLORS['shadow']};
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px {ModernUIStyles.COLORS['shadow']};
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            transform: translateY(0px);
            box-shadow: 0 2px 4px -1px {ModernUIStyles.COLORS['shadow']};
        }}
        
        QPushButton:disabled {{
            background: #9ca3af;
            color: #6b7280;
            box-shadow: none;
        }}
        """
    
    @staticmethod
    def get_success_button_style():
        """获取成功按钮样式"""
        return f"""
        QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            color: white;
            border: 2px solid #059669;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-height: 20px;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            border-color: #047857;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            border-color: #065f46;
            transform: translateY(0px);
            box-shadow: 0 2px 4px -1px rgba(16, 185, 129, 0.2);
        }}
        
        QPushButton:disabled {{
            background: #9ca3af;
            color: #6b7280;
            border-color: #9ca3af;
            box-shadow: none;
        }}
        """
    
    @staticmethod
    def get_modern_input_style():
        """获取现代化输入框样式"""
        return f"""
        QLineEdit, QSpinBox, QDoubleSpinBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 11pt;
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {ModernUIStyles.COLORS['primary']};
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }}
        
        QLineEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover {{
            border-color: #cbd5e1;
        }}
        """
    
    @staticmethod
    def get_modern_combobox_style():
        """获取现代化下拉框样式"""
        return f"""
        QComboBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 11pt;
            color: {ModernUIStyles.COLORS['text_primary']};
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernUIStyles.COLORS['primary']};
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }}
        
        QComboBox:hover {{
            border-color: #cbd5e1;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernUIStyles.COLORS['text_secondary']};
            margin-right: 10px;
        }}
        
        QComboBox QAbstractItemView {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            selection-background-color: {ModernUIStyles.COLORS['primary']};
            selection-color: white;
            padding: 4px;
        }}
        """
    
    @staticmethod
    def get_modern_checkbox_style():
        """获取现代化复选框样式"""
        return f"""
        QCheckBox {{
            font-weight: 600;
            color: {ModernUIStyles.COLORS['text_primary']};
            spacing: 10px;
            font-size: 11pt;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 10px;
            border: 2px solid {ModernUIStyles.COLORS['border']};
            background: {ModernUIStyles.COLORS['surface']};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {ModernUIStyles.COLORS['primary']};
        }}
        
        QCheckBox::indicator:checked {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            border-color: {ModernUIStyles.COLORS['success']};
        }}
        
        QCheckBox::indicator:checked:hover {{
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }}
        """

    @staticmethod
    def get_modern_slider_style():
        """获取现代化滑块样式"""
        return f"""
        QSlider::groove:horizontal {{
            border: 1px solid {ModernUIStyles.COLORS['border']};
            height: 8px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border: 2px solid {ModernUIStyles.COLORS['primary']};
            width: 20px;
            height: 20px;
            margin: -7px 0;
            border-radius: 10px;
            box-shadow: 0 2px 4px -1px {ModernUIStyles.COLORS['shadow']};
        }}

        QSlider::handle:horizontal:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            border-color: #5a67d8;
            box-shadow: 0 4px 6px -1px {ModernUIStyles.COLORS['shadow']};
        }}

        QSlider::handle:horizontal:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
        }}
        """

    @staticmethod
    def get_modern_tab_style():
        """获取现代化标签页样式"""
        return f"""
        QTabWidget::pane {{
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 12px;
            background: {ModernUIStyles.COLORS['surface']};
            margin-top: -1px;
        }}

        QTabBar::tab {{
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: {ModernUIStyles.COLORS['text_secondary']};
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 600;
            font-size: 11pt;
            min-width: 100px;
        }}

        QTabBar::tab:selected {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border-bottom: 2px solid {ModernUIStyles.COLORS['primary']};
        }}

        QTabBar::tab:hover:!selected {{
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        """

    @staticmethod
    def get_modern_scrollbar_style():
        """获取现代化滚动条样式"""
        return f"""
        QScrollArea {{
            border: none;
            background: transparent;
        }}

        QScrollBar:vertical {{
            background: {ModernUIStyles.COLORS['surface']};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: transparent;
        }}

        QScrollBar:horizontal {{
            background: {ModernUIStyles.COLORS['surface']};
            height: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:horizontal {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border-radius: 6px;
            min-width: 20px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }}

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}

        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
            background: transparent;
        }}
        """

    @staticmethod
    def get_status_indicator_style(status="disconnected"):
        """获取状态指示器样式"""
        status_colors = {
            "connected": ModernUIStyles.COLORS['success'],
            "connecting": ModernUIStyles.COLORS['warning'],
            "disconnected": ModernUIStyles.COLORS['error'],
            "error": ModernUIStyles.COLORS['error']
        }

        color = status_colors.get(status, ModernUIStyles.COLORS['error'])

        return f"""
        QLabel {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {color};
            border-radius: 20px;
            padding: 8px 16px;
            font-weight: 600;
            color: {color};
            font-size: 11pt;
        }}
        """

    @staticmethod
    def get_glass_morphism_style():
        """获取玻璃拟态效果样式"""
        return f"""
        QFrame {{
            background: {ModernUIStyles.COLORS['glass']};
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }}
        """

    @staticmethod
    def get_neumorphism_style():
        """获取新拟物化效果样式"""
        return f"""
        QFrame {{
            background: {ModernUIStyles.COLORS['background']};
            border-radius: 16px;
            box-shadow:
                8px 8px 16px rgba(163, 177, 198, 0.6),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }}

        QFrame:pressed {{
            box-shadow:
                inset 4px 4px 8px rgba(163, 177, 198, 0.6),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }}
        """
