#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
碎片剪切次数测试 - 验证碎片数据范围控制剪切次数的功能
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QSpinBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QPolygon, QPoint

class FragmentCutsTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('✂️ 碎片剪切次数测试')
        self.setGeometry(300, 300, 900, 700)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("✂️ 碎片剪切次数功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 功能说明
        info = QLabel("""
        ✂️ 碎片数据范围的真正功能：
        
        📊 数值含义：
        • 最小值：对长方形进行随机剪切的最少次数
        • 最大值：对长方形进行随机剪切的最多次数
        • 每次颜色切换时，会在这个范围内随机选择剪切次数
        
        🎯 剪切效果：
        • 先绘制完整的长方形
        • 然后用背景色在长方形上进行随机剪切
        • 剪切形状包括：矩形、圆形、三角形
        • 剪切次数越多，长方形被"咬掉"的部分越多
        """)
        info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info)
        
        # 剪切次数控制
        control_layout = QHBoxLayout()
        control_layout.setSpacing(20)
        
        control_layout.addWidget(QLabel("剪切次数:"))
        
        self.cuts_spin = QSpinBox()
        self.cuts_spin.setRange(0, 50)
        self.cuts_spin.setValue(15)
        self.cuts_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
            }
        """)
        control_layout.addWidget(self.cuts_spin)
        
        test_btn = QPushButton("🔍 测试剪切效果")
        test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            }
        """)
        test_btn.clicked.connect(self.test_cuts)
        control_layout.addWidget(test_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 预设测试按钮
        preset_layout = QHBoxLayout()
        preset_layout.setSpacing(15)
        
        presets = [
            ("少量剪切", 5, "#10b981"),
            ("中等剪切", 15, "#f59e0b"), 
            ("大量剪切", 30, "#ef4444")
        ]
        
        for name, cuts, color in presets:
            btn = QPushButton(f"{name} ({cuts}次)")
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 10pt;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
            """)
            btn.clicked.connect(lambda checked, c=cuts: self.test_preset(c))
            preset_layout.addWidget(btn)
        
        preset_layout.addStretch()
        layout.addLayout(preset_layout)
        
        # 主程序测试
        main_btn = QPushButton("🚀 测试主程序完整功能")
        main_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        main_btn.clicked.connect(self.test_main_program)
        layout.addWidget(main_btn)
        
        # 结果显示
        self.result_label = QLabel("调整剪切次数并点击测试按钮查看效果")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
        
    def test_cuts(self):
        """测试指定剪切次数"""
        cuts = self.cuts_spin.value()
        self.create_test_window(cuts)
        
    def test_preset(self, cuts):
        """测试预设剪切次数"""
        self.cuts_spin.setValue(cuts)
        self.create_test_window(cuts)
        
    def create_test_window(self, cuts):
        """创建测试窗口"""
        self.test_window = QWidget()
        self.test_window.setWindowTitle(f"剪切次数测试 - {cuts}次")
        self.test_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # 设置窗口大小
        window_height = 600
        window_width = int(window_height * 9 / 16)
        self.test_window.resize(window_width, window_height)
        
        # 居中显示
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - window_width) // 2
            y = (screen_geometry.height() - window_height) // 2
            self.test_window.move(x, y)
        
        # 设置属性
        self.test_window.current_color = "#4f46e5"
        self.test_window.fragment_cuts = cuts
        
        # 绘制方法
        def paintEvent(event):
            try:
                painter = QPainter(self.test_window)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 背景
                painter.fillRect(self.test_window.rect(), QColor("#f0f0f0"))
                
                width = self.test_window.width()
                height = self.test_window.height()
                
                # 绘制基础长方形
                base_rect_width = width * 0.9
                base_rect_height = height * 0.8
                base_x = (width - base_rect_width) / 2
                base_y = (height - base_rect_height) / 2
                
                color = QColor(self.test_window.current_color)
                painter.setBrush(QBrush(color))
                painter.setPen(QPen(color.darker(120), 3))
                painter.drawRect(int(base_x), int(base_y), int(base_rect_width), int(base_rect_height))
                
                # 进行剪切
                import random
                random.seed(42)  # 固定种子确保一致性
                
                painter.setBrush(QBrush(QColor("#f0f0f0")))  # 背景色剪切
                painter.setPen(QPen(QColor("#f0f0f0"), 1))
                
                for i in range(self.test_window.fragment_cuts):
                    cut_type = random.randint(0, 2)
                    
                    if cut_type == 0:
                        # 矩形剪切
                        cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                        cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                        cut_w = random.uniform(15, 60)
                        cut_h = random.uniform(15, 60)
                        painter.drawRect(int(cut_x), int(cut_y), int(cut_w), int(cut_h))
                        
                    elif cut_type == 1:
                        # 圆形剪切
                        cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                        cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                        cut_size = random.uniform(20, 50)
                        painter.drawEllipse(int(cut_x), int(cut_y), int(cut_size), int(cut_size))
                        
                    else:
                        # 三角形剪切
                        cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                        cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                        cut_size = random.uniform(25, 55)
                        
                        triangle_points = [
                            QPoint(int(cut_x + cut_size/2), int(cut_y)),
                            QPoint(int(cut_x), int(cut_y + cut_size)),
                            QPoint(int(cut_x + cut_size), int(cut_y + cut_size))
                        ]
                        painter.drawPolygon(QPolygon(triangle_points))
                
                # 提示文字
                painter.setPen(QPen(QColor("white"), 2))
                painter.drawText(20, 30, f"剪切次数: {self.test_window.fragment_cuts}")
                painter.drawText(20, 50, "右键或ESC关闭")
                
            except Exception as e:
                print(f"绘制错误: {e}")
        
        # 事件处理
        def keyPressEvent(event):
            if event.key() == Qt.Key_Escape:
                self.test_window.close()
        
        def mousePressEvent(event):
            if event.button() == Qt.RightButton:
                self.test_window.close()
        
        # 绑定事件
        self.test_window.paintEvent = paintEvent
        self.test_window.keyPressEvent = keyPressEvent
        self.test_window.mousePressEvent = mousePressEvent
        
        # 显示窗口
        self.test_window.show()
        self.test_window.setFocus()
        self.test_window.raise_()
        
        # 更新结果显示
        self.result_label.setText(f"✂️ 剪切测试窗口已打开\n剪切次数: {cuts}\n观察长方形被剪切的效果")
        self.result_label.setStyleSheet("""
            QLabel {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                color: #92400e;
                min-height: 60px;
            }
        """)
        
        print(f"✅ 剪切测试窗口已创建，剪切次数: {cuts}")
        
    def test_main_program(self):
        """测试主程序"""
        try:
            from main_module import OBSControlApp
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            main_window = OBSControlApp()
            main_window.show()
            
            # 切换到爆闪播放器标签页
            tab_widget = main_window.tab_widget
            for i in range(tab_widget.count()):
                if "爆闪播放器" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
            
            self.result_label.setText("✅ 主程序已启动！\n请调整碎片数据范围测试剪切效果")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dbeafe;
                    border: 1px solid #3b82f6;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #1d4ed8;
                    min-height: 60px;
                }
            """)
            
        except Exception as e:
            self.result_label.setText(f"❌ 启动失败：{e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FragmentCutsTest()
    window.show()
    sys.exit(app.exec_())
