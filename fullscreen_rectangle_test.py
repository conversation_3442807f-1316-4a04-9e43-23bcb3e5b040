#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全屏长方形测试 - 验证长方形是否占满整个窗口
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QPolygon, QPoint

class FullscreenRectangleTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('📐 全屏长方形测试')
        self.setGeometry(300, 300, 800, 600)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("📐 全屏长方形功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 功能说明
        info = QLabel("""
        📐 全屏长方形功能说明：
        
        🎯 正常模式：
        • 显示占满整个窗口的完整长方形
        • 长方形从窗口边缘到边缘，没有边距
        • 颜色填满整个播放区域
        
        ✂️ 碎片模式：
        • 先绘制占满整个窗口的长方形
        • 然后用背景色进行随机剪切
        • 剪切范围覆盖整个窗口区域
        • 产生更大范围的破碎效果
        """)
        info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        
        # 正常模式测试
        normal_btn = QPushButton("📐 测试正常模式\n(全屏长方形)")
        normal_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 150px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        normal_btn.clicked.connect(lambda: self.test_mode(False))
        
        # 碎片模式测试
        fragment_btn = QPushButton("✂️ 测试碎片模式\n(全屏剪切)")
        fragment_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 150px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        fragment_btn.clicked.connect(lambda: self.test_mode(True))
        
        button_layout.addWidget(normal_btn)
        button_layout.addWidget(fragment_btn)
        layout.addLayout(button_layout)
        
        # 主程序测试按钮
        main_test_btn = QPushButton("🚀 测试主程序完整功能")
        main_test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        main_test_btn.clicked.connect(self.test_main_program)
        layout.addWidget(main_test_btn)
        
        # 结果显示
        self.result_label = QLabel("点击按钮测试全屏长方形效果")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
        
    def test_mode(self, is_fragment_mode):
        """测试指定模式"""
        # 创建测试窗口
        self.test_window = QWidget()
        mode_name = "碎片模式" if is_fragment_mode else "正常模式"
        self.test_window.setWindowTitle(f"全屏长方形 - {mode_name}")
        self.test_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # 设置9:16比例
        window_height = 800
        window_width = int(window_height * 9 / 16)
        self.test_window.resize(window_width, window_height)
        
        # 居中显示
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - window_width) // 2
            y = (screen_geometry.height() - window_height) // 2
            self.test_window.move(x, y)
        
        # 设置窗口属性
        self.test_window.current_color = "#4f46e5"
        self.test_window.is_fragment_mode = is_fragment_mode
        self.test_window.fragment_cuts = 75  # 测试用剪切次数
        
        # 绘制方法
        def paintEvent(event):
            try:
                painter = QPainter(self.test_window)
                painter.setRenderHint(QPainter.Antialiasing)
                
                width = self.test_window.width()
                height = self.test_window.height()
                
                if self.test_window.is_fragment_mode:
                    # 碎片模式：全屏长方形 + 剪切
                    # 先绘制占满整个窗口的长方形
                    color = QColor(self.test_window.current_color)
                    painter.setBrush(QBrush(color))
                    painter.setPen(QPen(color.darker(120), 3))
                    painter.drawRect(0, 0, width, height)
                    
                    # 然后进行剪切
                    import random
                    random.seed(42)  # 固定种子确保一致性
                    
                    painter.setBrush(QBrush(QColor("#f0f0f0")))  # 背景色剪切
                    painter.setPen(QPen(QColor("#f0f0f0"), 1))
                    
                    # 在整个窗口范围内进行剪切
                    for i in range(self.test_window.fragment_cuts):
                        cut_type = random.randint(0, 3)  # 简化的剪切类型
                        
                        if cut_type == 0:
                            # 矩形剪切
                            cut_x = random.uniform(0, width * 0.9)
                            cut_y = random.uniform(0, height * 0.9)
                            cut_w = random.uniform(5, 80)
                            cut_h = random.uniform(5, 80)
                            painter.drawRect(int(cut_x), int(cut_y), int(cut_w), int(cut_h))
                            
                        elif cut_type == 1:
                            # 圆形剪切
                            cut_x = random.uniform(0, width * 0.9)
                            cut_y = random.uniform(0, height * 0.9)
                            cut_size = random.uniform(10, 60)
                            painter.drawEllipse(int(cut_x), int(cut_y), int(cut_size), int(cut_size))
                            
                        elif cut_type == 2:
                            # 三角形剪切
                            cut_x = random.uniform(0, width * 0.9)
                            cut_y = random.uniform(0, height * 0.9)
                            
                            triangle_points = [
                                QPoint(int(cut_x + random.uniform(-20, 40)), int(cut_y)),
                                QPoint(int(cut_x), int(cut_y + random.uniform(20, 60))),
                                QPoint(int(cut_x + random.uniform(20, 60)), int(cut_y + random.uniform(20, 60)))
                            ]
                            painter.drawPolygon(QPolygon(triangle_points))
                            
                        else:
                            # 随机多边形剪切
                            cut_x = random.uniform(0, width * 0.9)
                            cut_y = random.uniform(0, height * 0.9)
                            point_count = random.randint(4, 8)
                            
                            polygon_points = []
                            for j in range(point_count):
                                px = cut_x + random.uniform(-30, 50)
                                py = cut_y + random.uniform(-30, 50)
                                polygon_points.append(QPoint(int(px), int(py)))
                            
                            painter.drawPolygon(QPolygon(polygon_points))
                else:
                    # 正常模式：占满整个窗口的长方形
                    color = QColor(self.test_window.current_color)
                    painter.setBrush(QBrush(color))
                    painter.setPen(QPen(color.darker(120), 3))
                    painter.drawRect(0, 0, width, height)
                
                # 提示文字
                painter.setPen(QPen(QColor("white"), 2))
                painter.drawText(20, 30, f"模式: {mode_name}")
                painter.drawText(20, 50, f"窗口尺寸: {width} × {height}")
                painter.drawText(20, 70, "长方形占满整个窗口")
                painter.drawText(20, height - 20, "右键或ESC关闭")
                
            except Exception as e:
                print(f"绘制错误: {e}")
        
        # 事件处理
        def keyPressEvent(event):
            if event.key() == Qt.Key_Escape:
                self.test_window.close()
        
        def mousePressEvent(event):
            if event.button() == Qt.RightButton:
                self.test_window.close()
        
        # 绑定事件
        self.test_window.paintEvent = paintEvent
        self.test_window.keyPressEvent = keyPressEvent
        self.test_window.mousePressEvent = mousePressEvent
        
        # 显示窗口
        self.test_window.show()
        self.test_window.setFocus()
        self.test_window.raise_()
        
        # 更新结果显示
        self.result_label.setText(f"📐 {mode_name}测试窗口已打开\n长方形现在占满整个窗口，没有边距")
        self.result_label.setStyleSheet("""
            QLabel {
                background: #dcfce7;
                border: 1px solid #16a34a;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                color: #15803d;
                min-height: 60px;
            }
        """)
        
        print(f"✅ {mode_name}全屏长方形测试窗口已创建")
        
    def test_main_program(self):
        """测试主程序"""
        try:
            from main_module import OBSControlApp
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            main_window = OBSControlApp()
            main_window.show()
            
            # 切换到爆闪播放器标签页
            tab_widget = main_window.tab_widget
            for i in range(tab_widget.count()):
                if "爆闪播放器" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
            
            self.result_label.setText("✅ 主程序已启动！\n现在长方形会占满整个播放窗口")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dbeafe;
                    border: 1px solid #3b82f6;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #1d4ed8;
                    min-height: 60px;
                }
            """)
            
        except Exception as e:
            self.result_label.setText(f"❌ 启动失败：{e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FullscreenRectangleTest()
    window.show()
    sys.exit(app.exec_())
