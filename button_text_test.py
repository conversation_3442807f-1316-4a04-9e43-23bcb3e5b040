#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
按钮文字显示测试 - 确保颜色管理按钮文字完全可见
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox
)
from PyQt5.QtCore import Qt

class ButtonTextTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🔤 按钮文字显示测试')
        self.setGeometry(200, 200, 800, 500)
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 标题
        title = QLabel("🔤 颜色管理按钮文字显示测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 测试面板
        test_panel = self.create_test_panel()
        main_layout.addWidget(test_panel)
        
        # 说明
        info = QLabel("✅ 如果所有按钮文字都完全可见，说明修复成功！")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                background: #dcfce7;
                border-radius: 8px;
                border: 1px solid #16a34a;
            }
        """)
        main_layout.addWidget(info)
        
        self.setLayout(main_layout)
        
    def create_test_panel(self):
        """创建测试面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)  # 调整后的边距
        layout.setSpacing(12)  # 调整后的间距
        
        # 颜色管理组
        color_group = QGroupBox("🎨 颜色管理")
        color_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        color_layout = QVBoxLayout(color_group)
        
        # 按钮布局 - 使用修复后的样式
        color_select_layout = QHBoxLayout()
        color_select_layout.setSpacing(8)  # 调整间距
        
        # 选择颜色按钮
        color_picker_btn = QPushButton("🎨 选择颜色")
        color_picker_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                max-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        
        # 添加颜色按钮
        add_color_btn = QPushButton("➕ 添加颜色")
        add_color_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                max-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        
        # 清空颜色按钮
        clear_colors_btn = QPushButton("🗑️ 清空颜色")
        clear_colors_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                max-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        
        color_select_layout.addWidget(color_picker_btn)
        color_select_layout.addWidget(add_color_btn)
        color_select_layout.addWidget(clear_colors_btn)
        color_layout.addLayout(color_select_layout)
        
        # 当前颜色显示
        current_color_display = QLabel("当前颜色: 未选择")
        current_color_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                background: #f1f5f9;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                font-weight: bold;
                margin-top: 8px;
            }
        """)
        color_layout.addWidget(current_color_display)
        
        layout.addWidget(color_group)
        
        # 测试不同宽度的容器
        test_widths = [300, 250, 200]
        for i, width in enumerate(test_widths):
            test_group = QGroupBox(f"📏 测试宽度: {width}px")
            test_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    font-size: 11pt;
                    color: #1e293b;
                    border: 2px solid #cbd5e1;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    background: white;
                }
            """)
            test_group.setMaximumWidth(width)
            
            test_layout = QHBoxLayout(test_group)
            test_layout.setSpacing(5)
            
            # 创建相同的按钮但宽度受限
            for btn_text, color in [
                ("🎨 选择颜色", "#667eea"),
                ("➕ 添加颜色", "#10b981"),
                ("🗑️ 清空颜色", "#ef4444")
            ]:
                btn = QPushButton(btn_text)
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background: {color};
                        color: white;
                        border: none;
                        padding: 8px 10px;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 9pt;
                        min-width: 60px;
                    }}
                """)
                test_layout.addWidget(btn)
            
            layout.addWidget(test_group)
        
        return panel

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ButtonTextTest()
    window.show()
    sys.exit(app.exec_())
