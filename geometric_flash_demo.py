#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import random
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox, QListWidget, QSpinBox, QFormLayout,
    QCheckBox, QRadioButton, QColorDialog, QMessageBox, QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPoint
from PyQt5.QtGui import QPainter, QPolygon, QBrush, QPen, QColor

class GeometricFlashWindow(QWidget):
    """几何形状爆闪播放窗口"""
    
    close_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_color = "#ff0000"  # 默认红色
        self.shapes = []  # 存储当前的几何形状
        self.generate_random_shapes()
        
    def generate_random_shapes(self):
        """生成随机几何形状"""
        self.shapes = []
        width = 1920  # 假设全屏宽度
        height = 1080  # 假设全屏高度
        
        # 生成3-8个随机多边形
        num_shapes = random.randint(3, 8)
        
        for _ in range(num_shapes):
            # 生成随机多边形
            num_points = random.randint(3, 8)  # 3-8个顶点
            points = []
            
            # 随机生成多边形的中心点
            center_x = random.randint(width // 4, 3 * width // 4)
            center_y = random.randint(height // 4, 3 * height // 4)
            
            # 生成围绕中心点的随机顶点
            for i in range(num_points):
                angle = (2 * 3.14159 * i) / num_points + random.uniform(-0.5, 0.5)
                radius = random.randint(100, 400)
                
                x = center_x + int(radius * random.uniform(0.3, 1.8) * (1 if random.random() > 0.5 else -1))
                y = center_y + int(radius * random.uniform(0.3, 1.8) * (1 if random.random() > 0.5 else -1))
                
                # 确保点在屏幕范围内
                x = max(0, min(width, x))
                y = max(0, min(height, y))
                
                points.append(QPoint(x, y))
            
            self.shapes.append(QPolygon(points))
    
    def update_color_and_shape(self, color):
        """更新颜色和重新生成形状"""
        self.current_color = color
        self.generate_random_shapes()  # 每次切换颜色时生成新的形状
        self.update()  # 触发重绘
    
    def paintEvent(self, event):
        """绘制几何形状"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置背景色为浅灰色
        painter.fillRect(self.rect(), QColor("#f0f0f0"))
        
        # 设置画笔和画刷
        color = QColor(self.current_color)
        painter.setBrush(QBrush(color))
        painter.setPen(QPen(color.darker(120), 2))
        
        # 绘制所有形状
        for shape in self.shapes:
            painter.drawPolygon(shape)
        
        # 添加一些随机线条作为装饰
        painter.setPen(QPen(color.darker(150), 3))
        for _ in range(random.randint(8, 20)):
            x1 = random.randint(0, self.width())
            y1 = random.randint(0, self.height())
            x2 = random.randint(0, self.width())
            y2 = random.randint(0, self.height())
            painter.drawLine(x1, y1, x2, y2)
        
        # 添加提示文字
        painter.setPen(QPen(QColor("white"), 2))
        painter.drawText(50, 50, "按 ESC 键或右键关闭播放器")
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Escape:
            self.close_requested.emit()
        super().keyPressEvent(event)
    
    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        if event.button() == Qt.RightButton:
            self.close_requested.emit()
        super().mousePressEvent(event)

class GeometricFlashDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('⚡ 几何形状爆闪播放器演示')
        self.setGeometry(200, 200, 900, 700)
        
        # 初始化状态
        self.flash_player_state = {
            "colors": ["#ff0000", "#00ff00", "#0000ff"],  # 预设一些颜色
            "is_playing": False,
            "play_mode": "顺序轮播",
            "ignore_interval": False,
            "min_interval": 500,
            "max_interval": 2000,
            "fragment_min": 10,
            "fragment_max": 30,
            "current_color_index": 0,
            "flash_window": None,
            "flash_timer": QTimer(self)
        }
        
        self.selected_color = None
        self.flash_player_state["flash_timer"].timeout.connect(self.flash_next_color)
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("⚡ 几何形状爆闪播放器")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 说明文字
        desc = QLabel("✨ 这个播放器会生成随机的几何形状，并根据您选择的颜色进行无规则变化")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("color: #64748b; font-size: 12pt; padding: 10px;")
        main_layout.addWidget(desc)
        
        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        content_layout.addWidget(left_panel, 1)
        
        # 右侧显示面板
        right_panel = self.create_display_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        self.start_btn = QPushButton("🚀 启动几何形状轮播")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        self.start_btn.clicked.connect(self.start_flash)
        
        self.stop_btn = QPushButton("⏹️ 停止轮播")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        self.stop_btn.clicked.connect(self.stop_flash)
        self.stop_btn.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
        # 初始化颜色列表显示
        self.update_colors_list()
        
    def create_control_panel(self):
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 颜色管理
        color_group = QGroupBox("🎨 颜色管理")
        color_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        color_layout = QVBoxLayout(color_group)
        
        # 颜色按钮
        color_btn_layout = QHBoxLayout()
        self.pick_color_btn = QPushButton("🎨 选择颜色")
        self.pick_color_btn.clicked.connect(self.pick_color)
        self.add_color_btn = QPushButton("➕ 添加颜色")
        self.add_color_btn.clicked.connect(self.add_color)
        self.clear_colors_btn = QPushButton("🗑️ 清空颜色")
        self.clear_colors_btn.clicked.connect(self.clear_colors)
        
        color_btn_layout.addWidget(self.pick_color_btn)
        color_btn_layout.addWidget(self.add_color_btn)
        color_btn_layout.addWidget(self.clear_colors_btn)
        color_layout.addLayout(color_btn_layout)
        
        # 当前颜色显示
        self.current_color_display = QLabel("当前颜色: 未选择")
        self.current_color_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                background: #f1f5f9;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                font-weight: bold;
            }
        """)
        color_layout.addWidget(self.current_color_display)
        
        layout.addWidget(color_group)
        
        # 轮播模式
        mode_group = QGroupBox("🔄 轮播模式")
        mode_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        mode_layout = QVBoxLayout(mode_group)
        
        self.mode_sequential = QRadioButton("顺序轮播")
        self.mode_sequential.setChecked(True)
        self.mode_random = QRadioButton("随机乱序轮播")
        
        mode_layout.addWidget(self.mode_sequential)
        mode_layout.addWidget(self.mode_random)
        
        layout.addWidget(mode_group)
        
        return panel

    def create_display_panel(self):
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 无视间隔设置
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)

        self.ignore_interval = QCheckBox("自由无视间隔剪切")
        interval_layout.addWidget(self.ignore_interval)

        # 碎片数据范围
        fragment_layout = QFormLayout()

        self.fragment_min_spin = QSpinBox()
        self.fragment_min_spin.setRange(1, 100)
        self.fragment_min_spin.setValue(10)
        fragment_layout.addRow("最小:", self.fragment_min_spin)

        self.fragment_max_spin = QSpinBox()
        self.fragment_max_spin.setRange(1, 100)
        self.fragment_max_spin.setValue(30)
        fragment_layout.addRow("最大:", self.fragment_max_spin)

        interval_layout.addLayout(fragment_layout)
        layout.addWidget(interval_group)

        # 轮播设置
        carousel_group = QGroupBox("🎛️ 轮播设置【毫秒】")
        carousel_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        carousel_layout = QFormLayout(carousel_group)

        self.min_interval_spin = QSpinBox()
        self.min_interval_spin.setRange(10, 10000)
        self.min_interval_spin.setValue(500)
        self.min_interval_spin.setSuffix(" ms")
        carousel_layout.addRow("最小:", self.min_interval_spin)

        self.max_interval_spin = QSpinBox()
        self.max_interval_spin.setRange(10, 10000)
        self.max_interval_spin.setValue(2000)
        self.max_interval_spin.setSuffix(" ms")
        carousel_layout.addRow("最大:", self.max_interval_spin)

        layout.addWidget(carousel_group)

        # 已添加颜色
        colors_group = QGroupBox("🎨 已添加颜色")
        colors_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        colors_layout = QVBoxLayout(colors_group)

        self.colors_list = QListWidget()
        self.colors_list.setMaximumHeight(150)
        colors_layout.addWidget(self.colors_list)

        layout.addWidget(colors_group)

        return panel

    def pick_color(self):
        color = QColorDialog.getColor()
        if color.isValid():
            self.selected_color = color
            color_name = color.name()
            self.current_color_display.setText(f"当前颜色: {color_name}")
            self.current_color_display.setStyleSheet(f"""
                QLabel {{
                    padding: 8px;
                    background: {color_name};
                    border-radius: 6px;
                    color: {'white' if color.lightness() < 128 else 'black'};
                    font-weight: bold;
                }}
            """)

    def add_color(self):
        if hasattr(self, 'selected_color') and self.selected_color and self.selected_color.isValid():
            color_name = self.selected_color.name()
            if color_name not in self.flash_player_state["colors"]:
                self.flash_player_state["colors"].append(color_name)
                self.update_colors_list()
                print(f"添加颜色: {color_name}")
            else:
                QMessageBox.information(self, "提示", "该颜色已存在！")
        else:
            QMessageBox.warning(self, "警告", "请先选择一个颜色！")

    def clear_colors(self):
        reply = QMessageBox.question(self, "确认", "确定要清空所有颜色吗？")
        if reply == QMessageBox.Yes:
            self.flash_player_state["colors"].clear()
            self.update_colors_list()

    def update_colors_list(self):
        self.colors_list.clear()
        for color in self.flash_player_state["colors"]:
            item = QListWidgetItem(f"🎨 {color}")
            self.colors_list.addItem(item)

    def start_flash(self):
        if not self.flash_player_state["colors"]:
            QMessageBox.warning(self, "警告", "请先添加至少一个颜色！")
            return

        # 更新设置
        self.flash_player_state["min_interval"] = self.min_interval_spin.value()
        self.flash_player_state["max_interval"] = self.max_interval_spin.value()
        self.flash_player_state["ignore_interval"] = self.ignore_interval.isChecked()
        self.flash_player_state["fragment_min"] = self.fragment_min_spin.value()
        self.flash_player_state["fragment_max"] = self.fragment_max_spin.value()
        self.flash_player_state["play_mode"] = "顺序轮播" if self.mode_sequential.isChecked() else "随机乱序轮播"

        self.create_flash_window()
        self.flash_player_state["is_playing"] = True
        self.flash_player_state["current_color_index"] = 0

        self.flash_next_color()

        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        print("几何形状爆闪播放器已启动")

    def stop_flash(self):
        self.flash_player_state["is_playing"] = False
        self.flash_player_state["flash_timer"].stop()

        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].close()
            self.flash_player_state["flash_window"] = None

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        print("几何形状爆闪播放器已停止")

    def create_flash_window(self):
        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].close()

        flash_window = GeometricFlashWindow()
        flash_window.setWindowTitle("几何形状爆闪播放器")
        flash_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        flash_window.showFullScreen()

        # 连接关闭信号
        flash_window.close_requested.connect(self.stop_flash)

        self.flash_player_state["flash_window"] = flash_window

    def flash_next_color(self):
        if not self.flash_player_state["is_playing"] or not self.flash_player_state["colors"]:
            return

        colors = self.flash_player_state["colors"]

        if self.flash_player_state["play_mode"] == "顺序轮播":
            color = colors[self.flash_player_state["current_color_index"]]
            self.flash_player_state["current_color_index"] = (self.flash_player_state["current_color_index"] + 1) % len(colors)
        else:
            color = random.choice(colors)

        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].update_color_and_shape(color)

        # 设置下次切换时间
        if not self.flash_player_state["ignore_interval"]:
            interval = random.randint(
                self.flash_player_state["min_interval"],
                self.flash_player_state["max_interval"]
            )
            self.flash_player_state["flash_timer"].start(interval)
        else:
            # 使用碎片数据范围
            fragment_interval = random.randint(
                self.flash_player_state["fragment_min"],
                self.flash_player_state["fragment_max"]
            )
            self.flash_player_state["flash_timer"].start(fragment_interval)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = GeometricFlashDemo()
    window.show()
    sys.exit(app.exec_())
