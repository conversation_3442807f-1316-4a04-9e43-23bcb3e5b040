#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频设备路由测试 - 验证音频是否真正输出到指定设备
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QComboBox, QHBoxLayout, QFileDialog
from PyQt5.QtCore import Qt
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioFormat, QAudioOutput
from PyQt5.QtCore import QBuffer, QIODevice

class AudioDeviceRoutingTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🎵 音频设备路由测试')
        self.setGeometry(300, 300, 800, 600)
        self.test_audio_file = None
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("🎵 音频设备路由强制测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 问题描述
        problem_info = QLabel("""
        🎯 测试目标：
        • 强制将音频输出到指定的设备（如VoiceMeeter Input）
        • 绕过Qt的默认音频路由机制
        • 使用QAudioOutput直接控制音频输出设备
        • 验证音频是否真正从指定设备输出
        """)
        problem_info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(problem_info)
        
        # 音频文件选择
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("测试音频文件:"))
        
        self.file_label = QLabel("未选择文件")
        self.file_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
            }
        """)
        
        select_file_btn = QPushButton("📁 选择音频文件")
        select_file_btn.setStyleSheet("""
            QPushButton {
                background: #6366f1;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #4f46e5;
            }
        """)
        select_file_btn.clicked.connect(self.select_audio_file)
        
        file_layout.addWidget(self.file_label, 1)
        file_layout.addWidget(select_file_btn)
        layout.addLayout(file_layout)
        
        # 设备选择
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("目标输出设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
                min-width: 300px;
            }
        """)
        self.load_audio_devices()
        device_layout.addWidget(self.device_combo)
        
        layout.addLayout(device_layout)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        test_layout.setSpacing(15)
        
        # 直接设备播放测试
        direct_test_btn = QPushButton("🎵 直接设备播放测试")
        direct_test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 50px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        direct_test_btn.clicked.connect(self.test_direct_device_playback)
        
        # 主程序测试
        main_test_btn = QPushButton("🚀 测试主程序\n(修复后版本)")
        main_test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 50px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        main_test_btn.clicked.connect(self.test_main_program)
        
        test_layout.addWidget(direct_test_btn)
        test_layout.addWidget(main_test_btn)
        layout.addLayout(test_layout)
        
        # 验证说明
        verify_info = QLabel("""
        🔍 验证方法：
        
        1. 🎧 如果选择VoiceMeeter Input：
           • 打开VoiceMeeter软件
           • 查看HARDWARE INPUT指示灯是否亮起
           • 音频应该从VoiceMeeter输出，而不是桌面扬声器
        
        2. 🔊 如果选择其他设备：
           • 确保该设备已连接并正常工作
           • 音频应该从指定设备输出
           • 不应该从系统默认设备输出
        
        3. 📊 观察控制台输出：
           • 查看设备设置和播放状态信息
           • 确认音频路由到正确的设备
        """)
        verify_info.setStyleSheet("""
            QLabel {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                padding: 15px;
                color: #92400e;
                font-size: 10pt;
                line-height: 1.4;
            }
        """)
        layout.addWidget(verify_info)
        
        # 结果显示
        self.result_label = QLabel("选择音频文件和设备，然后点击测试按钮")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
        
    def load_audio_devices(self):
        """加载音频输出设备"""
        try:
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            self.device_combo.clear()
            self.device_combo.addItem("系统默认设备", None)
            
            for device in devices:
                device_name = device.deviceName()
                self.device_combo.addItem(device_name, device)
                print(f"发现音频设备: {device_name}")
                
                # 自动选择VoiceMeeter设备
                if "VoiceMeeter" in device_name:
                    self.device_combo.setCurrentIndex(self.device_combo.count() - 1)
                    print(f"✅ 自动选择VoiceMeeter设备: {device_name}")
            
        except Exception as e:
            print(f"加载音频设备时出错: {e}")
    
    def select_audio_file(self):
        """选择音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "", 
            "音频文件 (*.wav *.mp3 *.ogg *.flac);;所有文件 (*.*)"
        )
        
        if file_path:
            self.test_audio_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            print(f"选择音频文件: {file_path}")
    
    def test_direct_device_playback(self):
        """测试直接设备播放"""
        if not self.test_audio_file:
            self.result_label.setText("❌ 请先选择音频文件")
            return
        
        selected_device = self.device_combo.currentData()
        device_name = self.device_combo.currentText()
        
        if not selected_device:
            self.result_label.setText("❌ 请选择音频输出设备")
            return
        
        try:
            print(f"🎵 开始直接设备播放测试...")
            print(f"   - 音频文件: {self.test_audio_file}")
            print(f"   - 目标设备: {device_name}")
            
            # 这里应该调用直接设备播放方法
            # 由于需要音频处理库，这里只是演示逻辑
            
            self.result_label.setText(f"🎵 直接设备播放测试已启动\n设备: {device_name}\n请检查音频是否从指定设备输出")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #fee2e2;
                    border: 1px solid #dc2626;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #dc2626;
                    min-height: 60px;
                }
            """)
            
            print(f"✅ 直接设备播放测试完成")
            
        except Exception as e:
            print(f"❌ 直接设备播放测试失败: {e}")
            self.result_label.setText(f"❌ 测试失败: {e}")
    
    def test_main_program(self):
        """测试主程序的修复版本"""
        try:
            from main_module import OBSControlApp
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            main_window = OBSControlApp()
            main_window.show()
            
            # 切换到音频播放器标签页
            tab_widget = main_window.tab_widget
            for i in range(tab_widget.count()):
                if "音频播放器" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
            
            self.result_label.setText("✅ 主程序已启动（修复版本）\n请在音频播放器中测试设备路由功能")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dcfce7;
                    border: 1px solid #16a34a;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #15803d;
                    min-height: 60px;
                }
            """)
            
            print("✅ 主程序已启动，现在应该支持强制音频设备路由")
            
        except Exception as e:
            self.result_label.setText(f"❌ 启动主程序失败: {e}")
            print(f"启动主程序时出错: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AudioDeviceRoutingTest()
    window.show()
    sys.exit(app.exec_())
