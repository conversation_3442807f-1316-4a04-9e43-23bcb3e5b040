#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
9:16比例播放窗口测试
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen

class TestRatioWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('9:16比例播放窗口测试')
        self.setGeometry(300, 300, 600, 400)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("🎬 9:16比例播放窗口测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("""
        📐 窗口比例说明：
        • 高度：800像素
        • 宽度：450像素 (800 × 9/16)
        • 比例：9:16 (竖屏比例)
        • 位置：屏幕居中显示
        • 样式：无边框，置顶显示
        """)
        info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info)
        
        # 测试按钮
        test_btn = QPushButton("🚀 测试9:16比例播放窗口")
        test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        test_btn.clicked.connect(self.test_ratio_window)
        layout.addWidget(test_btn)
        
        # 关闭按钮
        close_btn = QPushButton("❌ 关闭测试")
        close_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def test_ratio_window(self):
        """测试9:16比例播放窗口"""
        # 创建测试播放窗口
        self.test_window = QWidget()
        self.test_window.setWindowTitle("9:16比例播放窗口")
        self.test_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # 设置9:16比例的窗口大小
        window_height = 800
        window_width = int(window_height * 9 / 16)  # 450像素
        self.test_window.resize(window_width, window_height)
        
        # 居中显示窗口
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - window_width) // 2
            y = (screen_geometry.height() - window_height) // 2
            self.test_window.move(x, y)
        
        # 设置窗口属性
        self.test_window.current_color = "#ff0000"
        
        # 添加绘制方法
        def paintEvent(event):
            try:
                painter = QPainter(self.test_window)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 设置背景色
                painter.fillRect(self.test_window.rect(), QColor("#f0f0f0"))
                
                # 设置画笔和画刷
                color = QColor(self.test_window.current_color)
                painter.setBrush(QBrush(color))
                painter.setPen(QPen(color.darker(120), 3))
                
                # 绘制几何形状
                width = self.test_window.width()
                height = self.test_window.height()
                
                if width > 0 and height > 0:
                    import random
                    random.seed(42)  # 固定种子，确保形状一致
                    
                    # 绘制适合9:16比例的形状
                    for i in range(6):
                        x = random.randint(20, width - 120)
                        y = random.randint(20, height - 120)
                        w = random.randint(40, 100)
                        h = random.randint(40, 100)
                        
                        if i % 3 == 0:
                            painter.drawRect(x, y, w, h)
                        elif i % 3 == 1:
                            painter.drawEllipse(x, y, w, h)
                        else:
                            # 绘制三角形
                            from PyQt5.QtGui import QPolygon
                            from PyQt5.QtCore import QPoint
                            triangle = QPolygon([
                                QPoint(x + w//2, y),
                                QPoint(x, y + h),
                                QPoint(x + w, y + h)
                            ])
                            painter.drawPolygon(triangle)
                
                # 添加比例信息
                painter.setPen(QPen(QColor("white"), 2))
                painter.drawText(20, 30, f"窗口尺寸: {width} × {height}")
                painter.drawText(20, 50, f"比例: 9:16")
                painter.drawText(20, height - 40, "按 ESC 键或右键关闭")
                
            except Exception as e:
                print(f"绘制时出错: {e}")
        
        # 添加事件处理
        def keyPressEvent(event):
            if event.key() == Qt.Key_Escape:
                self.test_window.close()
        
        def mousePressEvent(event):
            if event.button() == Qt.RightButton:
                self.test_window.close()
        
        # 绑定事件
        self.test_window.paintEvent = paintEvent
        self.test_window.keyPressEvent = keyPressEvent
        self.test_window.mousePressEvent = mousePressEvent
        
        # 显示窗口
        self.test_window.show()
        self.test_window.setFocus()
        self.test_window.raise_()
        
        print(f"✅ 9:16比例播放窗口已创建")
        print(f"   - 宽度: {window_width}px")
        print(f"   - 高度: {window_height}px")
        print(f"   - 比例: 9:16")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestRatioWindow()
    window.show()
    sys.exit(app.exec_())
