#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主程序中的爆闪播放器功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 尝试导入主程序
    from main_module import OBSControlApp
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    
    def test_main_flash_player():
        """测试主程序中的爆闪播放器"""
        print("🧪 开始测试主程序中的爆闪播放器功能...")
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        # 创建主程序实例
        main_window = OBSControlApp()
        
        # 检查爆闪播放器相关属性是否存在
        required_attributes = [
            'flash_player_state',
            'fragment_min_spin',
            'fragment_max_spin',
            'ignore_interval_checkbox',
            'flash_start_btn',
            'flash_stop_btn'
        ]
        
        missing_attributes = []
        for attr in required_attributes:
            if not hasattr(main_window, attr):
                missing_attributes.append(attr)
        
        if missing_attributes:
            print(f"❌ 缺少以下属性: {missing_attributes}")
            return False
        
        # 检查碎片数据范围的初始值
        fragment_min = main_window.fragment_min_spin.value()
        fragment_max = main_window.fragment_max_spin.value()
        
        print(f"✅ 碎片数据范围初始值: 最小={fragment_min}, 最大={fragment_max}")
        
        # 检查爆闪播放器状态
        state = main_window.flash_player_state
        print(f"✅ 爆闪播放器状态: {len(state['colors'])} 个颜色")
        print(f"✅ 播放模式: {state['play_mode']}")
        print(f"✅ 无视间隔: {state['ignore_interval']}")
        print(f"✅ 碎片范围: {state['fragment_min']}-{state['fragment_max']}ms")
        
        # 显示主窗口
        main_window.show()
        
        # 切换到爆闪播放器标签页
        tab_widget = main_window.tab_widget
        for i in range(tab_widget.count()):
            if "爆闪播放器" in tab_widget.tabText(i):
                tab_widget.setCurrentIndex(i)
                print(f"✅ 已切换到爆闪播放器标签页 (索引: {i})")
                break
        else:
            print("❌ 未找到爆闪播放器标签页")
            return False
        
        print("🎉 主程序爆闪播放器功能测试完成！")
        print("💡 您现在可以:")
        print("   1. 选择和添加颜色")
        print("   2. 勾选'自由无视间隔剪切'启用碎片模式")
        print("   3. 调整碎片数据范围 (最小/最大值)")
        print("   4. 点击'启动几何形状轮播'开始播放")
        
        # 运行应用程序
        return app.exec_()
    
    if __name__ == '__main__':
        test_main_flash_player()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 main_module.py 文件存在且可以正常导入")
except Exception as e:
    print(f"❌ 测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()
