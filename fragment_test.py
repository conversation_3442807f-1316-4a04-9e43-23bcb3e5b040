#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox, QSpinBox, QFormLayout, QCheckBox
)
from PyQt5.QtCore import Qt

class FragmentTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('碎片数据范围测试')
        self.setGeometry(300, 300, 500, 400)
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("⚡ 碎片数据范围测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 无视间隔设置组
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)
        
        # 复选框
        self.ignore_interval_checkbox = QCheckBox("自由无视间隔剪切")
        self.ignore_interval_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                font-size: 11pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        interval_layout.addWidget(self.ignore_interval_checkbox)
        
        # 碎片数据范围设置
        fragment_group = QGroupBox("📊 碎片数据范围")
        fragment_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 11pt;
                color: #1e293b;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                background: white;
            }
        """)
        fragment_layout = QFormLayout(fragment_group)
        
        # 最小值设置
        self.fragment_min_spin = QSpinBox()
        self.fragment_min_spin.setRange(1, 100)
        self.fragment_min_spin.setValue(10)
        self.fragment_min_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        fragment_layout.addRow("最小:", self.fragment_min_spin)
        
        # 最大值设置
        self.fragment_max_spin = QSpinBox()
        self.fragment_max_spin.setRange(1, 100)
        self.fragment_max_spin.setValue(30)
        self.fragment_max_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        fragment_layout.addRow("最大:", self.fragment_max_spin)
        
        # 将碎片设置添加到间隔布局中
        interval_layout.addWidget(fragment_group)
        
        main_layout.addWidget(interval_group)
        
        # 说明文字
        info_label = QLabel("""
        <b>说明：</b><br>
        • 勾选"自由无视间隔剪切"后，将使用碎片数据范围进行快速切换<br>
        • 最小值和最大值控制切换间隔的毫秒数<br>
        • 数值越小，切换越快，视觉效果越"爆闪"<br>
        • 建议范围：10-50毫秒
        """)
        info_label.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 12px;
                color: #0c4a6e;
                font-size: 10pt;
            }
        """)
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("🧪 测试碎片设置")
        self.test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        self.test_btn.clicked.connect(self.test_fragment_settings)
        
        test_layout.addStretch()
        test_layout.addWidget(self.test_btn)
        test_layout.addStretch()
        
        main_layout.addLayout(test_layout)
        
        # 结果显示
        self.result_label = QLabel("点击测试按钮查看当前设置")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 10px;
                font-size: 11pt;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(self.result_label)
        
        self.setLayout(main_layout)
        
        # 连接信号
        self.ignore_interval_checkbox.stateChanged.connect(self.update_display)
        self.fragment_min_spin.valueChanged.connect(self.update_display)
        self.fragment_max_spin.valueChanged.connect(self.update_display)
        
        # 初始更新
        self.update_display()
        
    def update_display(self):
        """更新显示状态"""
        is_checked = self.ignore_interval_checkbox.isChecked()
        min_val = self.fragment_min_spin.value()
        max_val = self.fragment_max_spin.value()
        
        if is_checked:
            status = f"✅ 启用碎片模式 - 间隔范围: {min_val}-{max_val}毫秒"
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dcfce7;
                    border: 1px solid #16a34a;
                    border-radius: 6px;
                    padding: 10px;
                    font-size: 11pt;
                    font-weight: bold;
                    color: #15803d;
                }
            """)
        else:
            status = "❌ 碎片模式未启用 - 将使用正常轮播间隔"
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #fef2f2;
                    border: 1px solid #dc2626;
                    border-radius: 6px;
                    padding: 10px;
                    font-size: 11pt;
                    font-weight: bold;
                    color: #dc2626;
                }
            """)
        
        self.result_label.setText(status)
        
    def test_fragment_settings(self):
        """测试碎片设置"""
        is_enabled = self.ignore_interval_checkbox.isChecked()
        min_val = self.fragment_min_spin.value()
        max_val = self.fragment_max_spin.value()
        
        if is_enabled:
            message = f"""
            🎯 碎片设置测试结果：
            
            ✅ 碎片模式：已启用
            📊 最小间隔：{min_val} 毫秒
            📊 最大间隔：{max_val} 毫秒
            ⚡ 切换速度：{'极快' if max_val < 50 else '快速' if max_val < 200 else '中等'}
            
            这个设置将产生快速的颜色和形状变化效果！
            """
        else:
            message = """
            ❌ 碎片模式：未启用
            
            当前将使用正常的轮播间隔设置。
            要启用碎片模式，请勾选"自由无视间隔剪切"选项。
            """
        
        from PyQt5.QtWidgets import QMessageBox
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("碎片设置测试")
        msg_box.setText(message)
        msg_box.setStyleSheet("""
            QMessageBox {
                background: white;
                font-size: 11pt;
            }
            QMessageBox QLabel {
                color: #1e293b;
                padding: 10px;
            }
        """)
        msg_box.exec_()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FragmentTestWindow()
    window.show()
    sys.exit(app.exec_())
