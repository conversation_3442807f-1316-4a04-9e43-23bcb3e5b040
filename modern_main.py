# -*- coding: utf-8 -*-
"""
现代化UI主程序入口
提供选择使用原版UI或现代化UI的选项
"""

import sys
import os
import ctypes
from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont
import main_module
from modern_ui_styles import ModernUIStyles
from modern_main_window import ModernMainWindow
from modern_ui_config import ui_config

def resource_path(relative_path):
    """兼容 PyInstaller 单文件和开发环境的资源路径"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)

class UISelectionDialog(QDialog):
    """UI选择对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_ui = "modern"  # 默认选择现代化UI
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("选择界面风格")
        self.setFixedSize(500, 350)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 应用现代化样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {ModernUIStyles.COLORS['background']};
                color: {ModernUIStyles.COLORS['text_primary']};
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("🎨 选择界面风格")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 18pt;
                font-weight: 700;
                color: {ModernUIStyles.COLORS['primary']};
                text-align: center;
                margin-bottom: 10px;
            }}
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 描述
        description = QLabel("请选择您喜欢的界面风格，您可以随时在设置中更改")
        description.setStyleSheet(f"""
            QLabel {{
                font-size: 11pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                text-align: center;
                margin-bottom: 20px;
            }}
        """)
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # 选项按钮
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        
        # 现代化UI按钮
        modern_btn = QPushButton("✨ 现代化界面")
        modern_btn.setStyleSheet(f"""
            QPushButton {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 20px;
                font-weight: 600;
                font-size: 12pt;
                text-align: left;
            }}
            QPushButton:hover {{
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
                transform: translateY(0px);
            }}
        """)
        modern_btn.clicked.connect(lambda: self.select_ui("modern"))
        
        modern_desc = QLabel("• 科技感浅色主题\n• Neumorphism效果\n• 流畅动画过渡\n• 现代化组件设计")
        modern_desc.setStyleSheet(f"""
            QLabel {{
                color: white;
                font-size: 10pt;
                margin-top: 5px;
                line-height: 1.4;
            }}
        """)
        
        modern_container = QVBoxLayout()
        modern_container.addWidget(modern_btn)
        modern_container.addWidget(modern_desc)
        buttons_layout.addLayout(modern_container)
        
        # 经典UI按钮
        classic_btn = QPushButton("🔧 经典界面")
        classic_btn.setStyleSheet(f"""
            QPushButton {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
                border: 2px solid {ModernUIStyles.COLORS['border']};
                border-radius: 12px;
                padding: 20px;
                font-weight: 600;
                font-size: 12pt;
                text-align: left;
            }}
            QPushButton:hover {{
                background: {ModernUIStyles.COLORS['background']};
                border-color: {ModernUIStyles.COLORS['primary']};
            }}
            QPushButton:pressed {{
                background: #e2e8f0;
            }}
        """)
        classic_btn.clicked.connect(lambda: self.select_ui("classic"))
        
        classic_desc = QLabel("• 熟悉的传统界面\n• 稳定可靠的布局\n• 简洁实用的设计\n• 兼容性更好")
        classic_desc.setStyleSheet(f"""
            QLabel {{
                color: {ModernUIStyles.COLORS['text_secondary']};
                font-size: 10pt;
                margin-top: 5px;
                line-height: 1.4;
            }}
        """)
        
        classic_container = QVBoxLayout()
        classic_container.addWidget(classic_btn)
        classic_container.addWidget(classic_desc)
        buttons_layout.addLayout(classic_container)
        
        layout.addLayout(buttons_layout)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        
        # 记住选择复选框
        from PyQt5.QtWidgets import QCheckBox
        self.remember_choice = QCheckBox("记住我的选择")
        self.remember_choice.setStyleSheet(f"""
            QCheckBox {{
                color: {ModernUIStyles.COLORS['text_secondary']};
                font-size: 10pt;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
                background: {ModernUIStyles.COLORS['surface']};
            }}
            QCheckBox::indicator:checked {{
                background: {ModernUIStyles.COLORS['primary']};
                border-color: {ModernUIStyles.COLORS['primary']};
            }}
        """)
        bottom_layout.addWidget(self.remember_choice)
        bottom_layout.addStretch()
        
        layout.addLayout(bottom_layout)
    
    def select_ui(self, ui_type):
        """选择UI类型"""
        self.selected_ui = ui_type
        
        # 如果选择记住，保存到配置
        if self.remember_choice.isChecked():
            ui_config.set('ui.preferred_style', ui_type)
            ui_config.save_config()
        
        self.accept()

def setup_application_style(app):
    """设置应用程序样式"""
    try:
        # 应用全局样式
        app.setStyleSheet(f"""
            * {{
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            }}
            
            QApplication {{
                background: {ModernUIStyles.COLORS['background']};
            }}
            
            QToolTip {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
                border: 1px solid {ModernUIStyles.COLORS['border']};
                border-radius: 6px;
                padding: 8px;
                font-size: 10pt;
            }}
            
            QMessageBox {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
            }}
            
            QMessageBox QPushButton {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                min-width: 80px;
            }}
            
            QMessageBox QPushButton:hover {{
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }}
        """)
        
        # 设置字体
        font = QFont("Inter", 11)
        if not font.exactMatch():
            font = QFont("Noto Sans SC", 11)
            if not font.exactMatch():
                font = QFont("Microsoft YaHei", 11)
        
        app.setFont(font)
        
    except Exception as e:
        print(f"设置应用程序样式失败: {e}")

def main():
    """主函数"""
    # 设置应用程序ID
    app_id = 'com.haohnb.obscontroller.2.0'
    try:
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
    except Exception as e:
        print(f"设置 AppUserModelID 失败: {e}")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    setup_application_style(app)
    
    # 设置图标
    icon_path = resource_path('obs2.ico')
    if not os.path.exists(icon_path):
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'obs2.ico')
    
    if os.path.exists(icon_path):
        try:
            app_icon = QIcon(icon_path)
            app.setWindowIcon(app_icon)
            print(f"成功加载图标: {icon_path}")
        except Exception as e:
            print(f"设置图标失败: {e}")
    else:
        print(f"找不到图标文件: {icon_path}")
    
    try:
        # 检查是否有保存的UI偏好
        preferred_ui = ui_config.get('ui.preferred_style', None)
        
        if preferred_ui is None:
            # 显示UI选择对话框
            dialog = UISelectionDialog()
            if dialog.exec_() == QDialog.Accepted:
                ui_type = dialog.selected_ui
            else:
                ui_type = "modern"  # 默认使用现代化UI
        else:
            ui_type = preferred_ui
        
        # 创建原始窗口实例（用于功能逻辑）
        original_window = main_module.MainWindow()
        
        if ui_type == "modern":
            # 使用现代化UI
            main_window = ModernMainWindow(original_window)
            main_window.show()
            
            # 隐藏原始窗口
            original_window.hide()
            
            print("启动现代化界面")
        else:
            # 使用经典UI
            main_window = original_window
            if os.path.exists(icon_path):
                main_window.setWindowIcon(QIcon(icon_path))
            main_window.show()
            
            print("启动经典界面")
        
        # 执行激活检查
        original_window.check_and_handle_activation()
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"启动应用程序时出错: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        error_dialog = QMessageBox()
        error_dialog.setIcon(QMessageBox.Critical)
        error_dialog.setWindowTitle("启动错误")
        error_dialog.setText(f"应用程序启动失败:\n{str(e)}")
        error_dialog.setStandardButtons(QMessageBox.Ok)
        error_dialog.exec_()
        
        sys.exit(1)

if __name__ == '__main__':
    main()
