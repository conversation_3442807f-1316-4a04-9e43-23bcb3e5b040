# -*- coding: utf-8 -*-
"""
现代化UI组件库
包含各种现代化设计的UI组件
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox,
    QSlider, QSpinBox, QDoubleSpinBox, QFrame, QVBoxLayout, QHBoxLayout,
    QScrollArea, QFormLayout, QGroupBox, QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient
from modern_ui_styles import ModernUIStyles

class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title="", subtitle="", parent=None):
        super().__init__(parent)
        self.title = title
        self.subtitle = subtitle
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(15)
        self.layout.setContentsMargins(20, 20, 20, 20)
        
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14pt;
                    font-weight: 700;
                    color: {ModernUIStyles.COLORS['text_primary']};
                    margin-bottom: 5px;
                }}
            """)
            self.layout.addWidget(self.title_label)
        
        if self.subtitle:
            self.subtitle_label = QLabel(self.subtitle)
            self.subtitle_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 10pt;
                    color: {ModernUIStyles.COLORS['text_secondary']};
                    margin-bottom: 10px;
                }}
            """)
            self.layout.addWidget(self.subtitle_label)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_card_style())
    
    def add_widget(self, widget):
        """添加组件"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局"""
        self.layout.addLayout(layout)

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        self.setup_animations()
    
    def setup_style(self):
        """设置样式"""
        if self.button_type == "success":
            self.setStyleSheet(ModernUIStyles.get_success_button_style())
        else:
            self.setStyleSheet(ModernUIStyles.get_modern_button_style())
    
    def setup_animations(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

class ModernInput(QLineEdit):
    """现代化输入框组件"""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class ModernComboBox(QComboBox):
    """现代化下拉框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_combobox_style())

class ModernCheckBox(QCheckBox):
    """现代化复选框组件"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_checkbox_style())

class ModernSlider(QSlider):
    """现代化滑块组件"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_slider_style())

class ModernSpinBox(QSpinBox):
    """现代化数字输入框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class ModernDoubleSpinBox(QDoubleSpinBox):
    """现代化双精度数字输入框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class StatusIndicator(QLabel):
    """状态指示器组件"""
    
    def __init__(self, status="disconnected", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        self.update_status(self.status)
    
    def setup_animations(self):
        """设置动画效果"""
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self.pulse_animation)
        self.pulse_timer.start(1000)  # 每秒脉冲一次
    
    def update_status(self, status):
        """更新状态"""
        self.status = status
        status_texts = {
            "connected": "🟢 已连接",
            "connecting": "🟡 连接中...",
            "disconnected": "🔴 未连接",
            "error": "❌ 连接错误"
        }
        
        self.setText(status_texts.get(status, "❓ 未知状态"))
        self.setStyleSheet(ModernUIStyles.get_status_indicator_style(status))
    
    def pulse_animation(self):
        """脉冲动画"""
        if self.status == "connecting":
            # 添加脉冲效果
            pass

class GlassMorphismWidget(QFrame):
    """玻璃拟态效果组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_glass_morphism_style())

class NeumorphismWidget(QFrame):
    """新拟物化效果组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_neumorphism_style())

class FeatureCard(ModernCard):
    """功能卡片组件"""
    
    def __init__(self, title, description, icon="", parent=None):
        super().__init__(title, description, parent)
        self.icon = icon
        self.enabled = False
        self.setup_feature_ui()
    
    def setup_feature_ui(self):
        """设置功能UI"""
        # 添加启用开关
        self.enable_checkbox = ModernCheckBox(f"启用 {self.title}")
        self.enable_checkbox.stateChanged.connect(self.on_enable_changed)
        self.add_widget(self.enable_checkbox)
        
        # 添加配置区域
        self.config_widget = QWidget()
        self.config_layout = QVBoxLayout(self.config_widget)
        self.config_layout.setContentsMargins(0, 10, 0, 0)
        self.config_widget.setEnabled(False)
        self.add_widget(self.config_widget)
    
    def on_enable_changed(self, state):
        """启用状态改变"""
        self.enabled = state == Qt.Checked
        self.config_widget.setEnabled(self.enabled)
        
        # 添加动画效果
        if self.enabled:
            self.setStyleSheet(self.styleSheet() + f"""
                QFrame {{
                    border-color: {ModernUIStyles.COLORS['success']};
                }}
            """)
        else:
            self.setup_style()
    
    def add_config_widget(self, widget):
        """添加配置组件"""
        self.config_layout.addWidget(widget)

class ModernScrollArea(QScrollArea):
    """现代化滚动区域组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(ModernUIStyles.get_modern_scrollbar_style())
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

class ModernFormLayout(QFormLayout):
    """现代化表单布局"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSpacing(15)
        self.setContentsMargins(0, 0, 0, 0)
        self.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
    
    def addRow(self, label, field=None):
        """添加行"""
        if isinstance(label, str):
            label_widget = QLabel(label)
            label_widget.setStyleSheet(f"""
                QLabel {{
                    font-weight: 600;
                    color: {ModernUIStyles.COLORS['text_primary']};
                    font-size: 11pt;
                }}
            """)
            super().addRow(label_widget, field)
        else:
            super().addRow(label, field)

class ModernProgressBar(QProgressBar):
    """现代化进度条组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernUIStyles.COLORS['border']};
                border-radius: 8px;
                background: {ModernUIStyles.COLORS['surface']};
                text-align: center;
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
            }}
            
            QProgressBar::chunk {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border-radius: 6px;
                margin: 1px;
            }}
        """)
