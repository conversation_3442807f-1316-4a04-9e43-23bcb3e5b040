#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化UI代码语法检查
"""

import ast
import os

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 编译检查语法
        ast.parse(source)
        return True, "语法正确"
    
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """主函数"""
    print("🔍 检查现代化UI代码语法...")
    
    # 要检查的文件列表
    files_to_check = [
        'modern_ui_styles.py',
        'modern_ui_components.py', 
        'modern_main_window.py',
        'modern_ui_config.py',
        'modern_main.py',
        'test_modern_ui.py'
    ]
    
    all_passed = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            success, message = check_syntax(file_path)
            status = "✅" if success else "❌"
            print(f"{status} {file_path}: {message}")
            
            if not success:
                all_passed = False
        else:
            print(f"⚠️ {file_path}: 文件不存在")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有现代化UI文件语法检查通过！")
        print("\n📋 使用说明:")
        print("1. 运行 'python modern_main.py' 启动现代化界面")
        print("2. 运行 'python test_modern_ui.py' 测试UI组件")
        print("3. 运行 'python main.py' 使用原有界面")
    else:
        print("❌ 发现语法错误，请修复后再使用")
    
    return all_passed

if __name__ == '__main__':
    main()
