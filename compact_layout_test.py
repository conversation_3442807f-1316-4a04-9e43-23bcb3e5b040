#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
紧凑布局测试 - 移除标题和说明文字后的效果
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox, QSpinBox, QFormLayout, QCheckBox
)
from PyQt5.QtCore import Qt

class CompactLayoutTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('📐 紧凑布局测试 - 移除标题和说明文字')
        self.setGeometry(200, 200, 1000, 600)  # 减小高度
        self.initUI()
        
    def initUI(self):
        # 主布局 - 使用更紧凑的设置
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 10, 15, 10)  # 减小边距
        main_layout.setSpacing(10)  # 减小间距
        
        # 直接创建内容区域，不添加标题和说明
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        content_layout.addWidget(left_panel, 1)
        
        # 右侧面板 - 重点测试碎片数据范围显示
        right_panel = self.create_right_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        start_btn = QPushButton("🚀 启动几何形状轮播")
        start_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
        """)
        
        stop_btn = QPushButton("⏹️ 停止轮播")
        stop_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(start_btn)
        button_layout.addWidget(stop_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
    def create_left_panel(self):
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 颜色管理
        color_group = QGroupBox("🎨 颜色管理")
        color_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        color_layout = QVBoxLayout(color_group)
        
        # 模拟按钮
        btn_layout = QHBoxLayout()
        for text in ["🎨 选择", "➕ 添加", "🗑️ 清空"]:
            btn = QPushButton(text)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 8px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 9pt;
                    background: #f1f5f9;
                    border: 1px solid #cbd5e1;
                }
            """)
            btn_layout.addWidget(btn)
        color_layout.addLayout(btn_layout)
        
        # 当前颜色显示
        current_color = QLabel("当前颜色: 未选择")
        current_color.setStyleSheet("""
            QLabel {
                padding: 6px;
                background: #f1f5f9;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                font-size: 10pt;
            }
        """)
        color_layout.addWidget(current_color)
        
        layout.addWidget(color_group)
        
        # 轮播模式
        mode_group = QGroupBox("🔄 轮播模式")
        mode_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        mode_layout = QVBoxLayout(mode_group)
        
        mode1 = QLabel("○ 顺序轮播")
        mode2 = QLabel("○ 随机乱序轮播")
        mode1.setStyleSheet("font-size: 11pt; padding: 2px;")
        mode2.setStyleSheet("font-size: 11pt; padding: 2px;")
        mode_layout.addWidget(mode1)
        mode_layout.addWidget(mode2)
        
        layout.addWidget(mode_group)
        
        return panel
        
    def create_right_panel(self):
        """右侧面板 - 重点展示碎片数据范围的完整显示"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)  # 调整后的边距
        layout.setSpacing(10)  # 调整后的间距
        
        # 无视间隔设置
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)
        
        # 复选框
        checkbox = QCheckBox("自由无视间隔剪切")
        checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                font-size: 12pt;
            }
        """)
        interval_layout.addWidget(checkbox)
        
        # 碎片数据范围标题 - 紧凑样式
        fragment_title = QLabel("📊 碎片数据范围")
        fragment_title.setAlignment(Qt.AlignCenter)
        fragment_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #dc2626;
                font-size: 12pt;
                margin: 5px 0px;
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                padding: 6px;
                border-radius: 6px;
                border: 2px solid #dc2626;
            }
        """)
        interval_layout.addWidget(fragment_title)
        
        # 碎片设置容器 - 紧凑样式
        fragment_container = QWidget()
        fragment_container.setStyleSheet("""
            QWidget {
                background: linear-gradient(135deg, #fef9c3 0%, #fef08a 100%);
                border: 3px solid #eab308;
                border-radius: 12px;
                padding: 15px;
                margin: 2px;
            }
        """)
        fragment_layout = QFormLayout(fragment_container)
        fragment_layout.setSpacing(8)
        fragment_layout.setContentsMargins(10, 10, 10, 10)
        
        # 最小值
        min_label = QLabel("最小:")
        min_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        min_spin = QSpinBox()
        min_spin.setValue(10)
        min_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(min_label, min_spin)
        
        # 最大值
        max_label = QLabel("最大:")
        max_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        max_spin = QSpinBox()
        max_spin.setValue(30)
        max_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(max_label, max_spin)
        
        # 说明文字
        fragment_info = QLabel("💡 启用后使用上述数值进行快速切换(ms)")
        fragment_info.setWordWrap(True)
        fragment_info.setStyleSheet("""
            QLabel {
                color: #92400e;
                font-size: 9pt;
                font-style: italic;
                margin-top: 5px;
                padding: 3px;
                background: rgba(255, 255, 255, 0.7);
                border-radius: 3px;
            }
        """)
        fragment_layout.addRow(fragment_info)
        
        interval_layout.addWidget(fragment_container)
        layout.addWidget(interval_group)
        
        # 其他设置组 - 紧凑显示
        other_group = QGroupBox("🎛️ 轮播设置【毫秒】")
        other_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        other_layout = QVBoxLayout(other_group)
        other_content = QLabel("最小: 500ms\n最大: 2000ms")
        other_content.setStyleSheet("color: #64748b; font-size: 10pt; padding: 8px;")
        other_layout.addWidget(other_content)
        
        layout.addWidget(other_group)
        
        return panel

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = CompactLayoutTest()
    window.show()
    sys.exit(app.exec_())
