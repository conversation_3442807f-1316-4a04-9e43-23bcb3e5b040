# -*- coding: utf-8 -*-
"""
现代化主窗口
基于原有功能，采用现代化UI设计
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QScrollArea, QFrame, QSplitter, QApplication, QMessageBox,
    QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QIcon
from modern_ui_styles import ModernUIStyles
from modern_ui_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernCheckBox, ModernSlider, StatusIndicator, GlassMorphismWidget,
    ModernSpinBox, ModernDoubleSpinBox, FeatureCard, ModernScrollArea,
    ModernFormLayout
)

class ModernMainWindow(QWidget):
    """现代化主窗口类"""
    
    def __init__(self, original_window, parent=None):
        super().__init__(parent)
        self.original_window = original_window  # 保持对原窗口的引用
        self.setup_window()
        self.create_ui()
        self.setup_animations()
        self.connect_signals()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle('OBS 去重软件 v2.0 - 现代化版本')
        self.setGeometry(100, 50, 1200, 900)
        self.setMinimumSize(1200, 900)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('obs2.ico'))
        except:
            pass
        
        # 应用现代化样式
        self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_ui(self):
        """创建用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建头部区域
        self.create_header(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
    
    def create_header(self, parent_layout):
        """创建头部区域"""
        header_card = ModernCard()
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)
        
        # 应用标题和副标题
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)
        
        app_title = QLabel("🎬 OBS 去重软件")
        app_title.setStyleSheet(f"""
            QLabel {{
                font-size: 20pt;
                font-weight: 700;
                color: {ModernUIStyles.COLORS['primary']};
                margin: 0;
            }}
        """)
        
        app_subtitle = QLabel("现代化科技感界面 • 智能去重解决方案")
        app_subtitle.setStyleSheet(f"""
            QLabel {{
                font-size: 11pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                margin: 0;
            }}
        """)
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)
        header_layout.addLayout(title_layout)
        
        # 连接状态和控制按钮
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        
        # 状态指示器
        self.status_indicator = StatusIndicator("disconnected")
        controls_layout.addWidget(self.status_indicator)
        
        # 连接按钮
        self.connect_btn = ModernButton("🔗 连接到 OBS", "primary")
        self.connect_btn.clicked.connect(self.on_connect_clicked)
        controls_layout.addWidget(self.connect_btn)
        
        # 一键启动按钮
        self.quick_start_btn = ModernButton("🚀 一键启动", "success")
        self.quick_start_btn.clicked.connect(self.on_quick_start_clicked)
        self.quick_start_btn.setEnabled(False)
        controls_layout.addWidget(self.quick_start_btn)
        
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        header_card.add_layout(header_layout)
        parent_layout.addWidget(header_card)
    
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background: {ModernUIStyles.COLORS['border']};
                width: 2px;
            }}
            QSplitter::handle:hover {{
                background: {ModernUIStyles.COLORS['primary']};
            }}
        """)
        
        # 左侧：功能配置区域
        self.create_config_panel(splitter)
        
        # 右侧：主要功能区域
        self.create_function_tabs(splitter)
        
        # 设置分割比例
        splitter.setSizes([350, 850])
        parent_layout.addWidget(splitter)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_scroll = ModernScrollArea()
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)
        config_layout.setContentsMargins(10, 10, 10, 10)
        
        # 媒体源配置卡片
        media_card = ModernCard("🎯 媒体源配置", "选择要处理的视频和音频源")
        
        # 视频媒体源选择
        video_layout = QVBoxLayout()
        video_label = QLabel("视频媒体源:")
        video_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
                margin-bottom: 5px;
            }}
        """)
        self.video_source_combo = ModernComboBox()
        video_layout.addWidget(video_label)
        video_layout.addWidget(self.video_source_combo)
        
        # 音频媒体源选择
        audio_layout = QVBoxLayout()
        audio_label = QLabel("音频媒体源:")
        audio_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
                margin-bottom: 5px;
            }}
        """)
        self.audio_source_combo = ModernComboBox()
        audio_layout.addWidget(audio_label)
        audio_layout.addWidget(self.audio_source_combo)
        
        # 刷新按钮
        refresh_btn = ModernButton("🔄 刷新媒体源")
        refresh_btn.clicked.connect(self.on_refresh_sources)
        
        media_card.add_layout(video_layout)
        media_card.add_layout(audio_layout)
        media_card.add_widget(refresh_btn)
        config_layout.addWidget(media_card)
        
        # 快速设置卡片
        quick_settings_card = ModernCard("⚡ 快速设置", "常用功能的快速配置")
        
        # 预设配置
        preset_layout = QVBoxLayout()
        preset_label = QLabel("预设配置:")
        preset_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
                margin-bottom: 5px;
            }}
        """)
        self.preset_combo = ModernComboBox()
        self.preset_combo.addItems([
            "默认配置",
            "轻度去重",
            "中度去重", 
            "重度去重",
            "自定义配置"
        ])
        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.preset_combo)
        
        # 应用预设按钮
        apply_preset_btn = ModernButton("✨ 应用预设")
        apply_preset_btn.clicked.connect(self.on_apply_preset)
        
        quick_settings_card.add_layout(preset_layout)
        quick_settings_card.add_widget(apply_preset_btn)
        config_layout.addWidget(quick_settings_card)
        
        # 系统状态卡片
        status_card = ModernCard("📊 系统状态", "实时监控系统运行状态")
        
        # 状态信息
        self.cpu_label = QLabel("CPU 使用率: 0%")
        self.memory_label = QLabel("内存使用: 0 MB")
        self.connection_label = QLabel("连接状态: 未连接")
        
        status_labels = [self.cpu_label, self.memory_label, self.connection_label]
        for label in status_labels:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {ModernUIStyles.COLORS['text_secondary']};
                    font-size: 10pt;
                    padding: 5px;
                    background: {ModernUIStyles.COLORS['background']};
                    border-radius: 6px;
                    margin: 2px 0;
                }}
            """)
            status_card.add_widget(label)
        
        config_layout.addWidget(status_card)
        config_layout.addStretch()
        
        config_scroll.setWidget(config_widget)
        parent.addWidget(config_scroll)
    
    def create_function_tabs(self, parent):
        """创建功能标签页"""
        # 创建现代化标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(ModernUIStyles.get_modern_tab_style())
        
        # 视频去重标签页
        self.create_video_tab()
        
        # 音频去重标签页
        self.create_audio_tab()
        
        # 高级功能标签页
        self.create_advanced_tab()
        
        # 说明文档标签页
        self.create_help_tab()
        
        parent.addWidget(self.tab_widget)
    
    def create_video_tab(self):
        """创建视频去重标签页"""
        video_widget = QWidget()
        video_scroll = ModernScrollArea()
        video_content = QWidget()
        video_layout = QVBoxLayout(video_content)
        video_layout.setSpacing(20)
        video_layout.setContentsMargins(20, 20, 20, 20)
        
        # 智能加减速卡片
        speed_card = FeatureCard(
            "🚀 智能加减速",
            "根据视频播放状态自动调整播放速度，有效避免重复检测"
        )
        self.create_speed_controls(speed_card)
        video_layout.addWidget(speed_card)
        
        # 模糊去重卡片
        blur_card = FeatureCard(
            "🌫️ 模糊去重", 
            "通过动态调整模糊效果来改变视频特征"
        )
        self.create_blur_controls(blur_card)
        video_layout.addWidget(blur_card)
        
        # 移动去重卡片
        transform_card = FeatureCard(
            "↔️ 移动去重",
            "通过移动和缩放视频来改变画面特征"
        )
        self.create_transform_controls(transform_card)
        video_layout.addWidget(transform_card)
        
        # 颜色去重卡片
        color_card = FeatureCard(
            "🎨 颜色去重",
            "动态调整视频的颜色参数来避免重复检测"
        )
        self.create_color_controls(color_card)
        video_layout.addWidget(color_card)
        
        video_layout.addStretch()
        video_scroll.setWidget(video_content)
        self.tab_widget.addTab(video_scroll, "🎬 视频去重")
    
    def create_audio_tab(self):
        """创建音频去重标签页"""
        audio_widget = QWidget()
        audio_scroll = ModernScrollArea()
        audio_content = QWidget()
        audio_layout = QVBoxLayout(audio_content)
        audio_layout.setSpacing(20)
        audio_layout.setContentsMargins(20, 20, 20, 20)
        
        # 音频EQ去重卡片
        eq_card = FeatureCard(
            "🔊 音频EQ去重",
            "通过调整音频均衡器参数来改变音频特征"
        )
        self.create_audio_eq_controls(eq_card)
        audio_layout.addWidget(eq_card)
        
        # 断音控制卡片
        mute_card = FeatureCard(
            "🔇 断音控制",
            "随机在音频中插入短暂的静音来避免重复检测"
        )
        self.create_audio_mute_controls(mute_card)
        audio_layout.addWidget(mute_card)
        
        # 音量控制卡片
        volume_card = FeatureCard(
            "🔉 音量控制",
            "随机调整音频播放音量大小"
        )
        self.create_audio_volume_controls(volume_card)
        audio_layout.addWidget(volume_card)
        
        audio_layout.addStretch()
        audio_scroll.setWidget(audio_content)
        self.tab_widget.addTab(audio_scroll, "🎵 音频去重")
    
    def create_advanced_tab(self):
        """创建高级功能标签页"""
        advanced_widget = QWidget()
        advanced_scroll = ModernScrollArea()
        advanced_content = QWidget()
        advanced_layout = QVBoxLayout(advanced_content)
        advanced_layout.setSpacing(20)
        advanced_layout.setContentsMargins(20, 20, 20, 20)
        
        # 批量操作卡片
        batch_card = ModernCard("⚡ 批量操作", "一键执行多个去重功能")
        
        # 批量启动按钮
        batch_start_btn = ModernButton("🚀 批量启动所有功能", "success")
        batch_stop_btn = ModernButton("⏹️ 停止所有功能")
        
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(batch_start_btn)
        batch_layout.addWidget(batch_stop_btn)
        batch_card.add_layout(batch_layout)
        
        advanced_layout.addWidget(batch_card)
        advanced_layout.addStretch()
        
        advanced_scroll.setWidget(advanced_content)
        self.tab_widget.addTab(advanced_scroll, "⚙️ 高级功能")
    
    def create_help_tab(self):
        """创建帮助标签页"""
        help_widget = QWidget()
        help_scroll = ModernScrollArea()
        help_content = QWidget()
        help_layout = QVBoxLayout(help_content)
        help_layout.setSpacing(20)
        help_layout.setContentsMargins(20, 20, 20, 20)
        
        # 使用说明卡片
        help_card = ModernCard("📖 使用说明", "详细的功能介绍和使用指南")
        
        help_text = QLabel("""
        <h3>🎬 视频去重功能</h3>
        <p><b>智能加减速:</b> 自动调整视频播放速度，避免平台检测</p>
        <p><b>模糊去重:</b> 动态调整模糊效果，改变视频特征</p>
        <p><b>移动去重:</b> 通过移动和缩放改变画面布局</p>
        <p><b>颜色去重:</b> 调整色调、亮度等颜色参数</p>
        
        <h3>🎵 音频去重功能</h3>
        <p><b>音频EQ去重:</b> 调整音频均衡器参数</p>
        <p><b>断音控制:</b> 随机插入短暂静音</p>
        <p><b>音量控制:</b> 随机调整播放音量</p>
        
        <h3>⚙️ 使用步骤</h3>
        <p>1. 点击"连接到 OBS"建立连接</p>
        <p>2. 选择要处理的视频和音频媒体源</p>
        <p>3. 启用需要的去重功能并调整参数</p>
        <p>4. 点击"一键启动"开始去重处理</p>
        """)
        
        help_text.setStyleSheet(f"""
            QLabel {{
                color: {ModernUIStyles.COLORS['text_primary']};
                font-size: 11pt;
                line-height: 1.6;
                padding: 10px;
            }}
        """)
        help_text.setWordWrap(True)
        help_card.add_widget(help_text)
        
        help_layout.addWidget(help_card)
        help_layout.addStretch()
        
        help_scroll.setWidget(help_content)
        self.tab_widget.addTab(help_scroll, "📖 使用说明")

    def create_speed_controls(self, parent_card):
        """创建智能加减速控件"""
        form_layout = ModernFormLayout()

        # 最低速度
        min_speed_layout = QHBoxLayout()
        self.speed_min_slider = ModernSlider()
        self.speed_min_slider.setRange(1, 200)
        self.speed_min_slider.setValue(90)
        self.speed_min_spinbox = ModernSpinBox()
        self.speed_min_spinbox.setRange(1, 200)
        self.speed_min_spinbox.setValue(90)
        self.speed_min_spinbox.setSuffix(" %")

        self.speed_min_slider.valueChanged.connect(self.speed_min_spinbox.setValue)
        self.speed_min_spinbox.valueChanged.connect(self.speed_min_slider.setValue)

        min_speed_layout.addWidget(self.speed_min_slider)
        min_speed_layout.addWidget(self.speed_min_spinbox)
        form_layout.addRow("最低速度:", min_speed_layout)

        # 最高速度
        max_speed_layout = QHBoxLayout()
        self.speed_max_slider = ModernSlider()
        self.speed_max_slider.setRange(1, 200)
        self.speed_max_slider.setValue(120)
        self.speed_max_spinbox = ModernSpinBox()
        self.speed_max_spinbox.setRange(1, 200)
        self.speed_max_spinbox.setValue(120)
        self.speed_max_spinbox.setSuffix(" %")

        self.speed_max_slider.valueChanged.connect(self.speed_max_spinbox.setValue)
        self.speed_max_spinbox.valueChanged.connect(self.speed_max_slider.setValue)

        max_speed_layout.addWidget(self.speed_max_slider)
        max_speed_layout.addWidget(self.speed_max_spinbox)
        form_layout.addRow("最高速度:", max_speed_layout)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_blur_controls(self, parent_card):
        """创建模糊去重控件"""
        form_layout = ModernFormLayout()

        # 滤镜名称
        self.blur_filter_name = ModernInput("Composite Blur")
        form_layout.addRow("滤镜名称:", self.blur_filter_name)

        # 最小半径
        self.blur_min_radius = ModernDoubleSpinBox()
        self.blur_min_radius.setRange(0.0, 50.0)
        self.blur_min_radius.setSingleStep(0.1)
        self.blur_min_radius.setValue(0.0)
        form_layout.addRow("最小半径:", self.blur_min_radius)

        # 最大半径
        self.blur_max_radius = ModernDoubleSpinBox()
        self.blur_max_radius.setRange(0.0, 50.0)
        self.blur_max_radius.setSingleStep(0.1)
        self.blur_max_radius.setValue(2.0)
        form_layout.addRow("最大半径:", self.blur_max_radius)

        # 变化间隔
        self.blur_interval = ModernSpinBox()
        self.blur_interval.setRange(100, 10000)
        self.blur_interval.setValue(1000)
        self.blur_interval.setSuffix(" ms")
        form_layout.addRow("变化间隔:", self.blur_interval)

        # 下载链接
        download_label = QLabel('<a href="#" style="color: #667eea; text-decoration: underline;">📥 下载模糊插件</a>')
        download_label.setOpenExternalLinks(False)
        download_label.linkActivated.connect(self.on_download_blur_plugin)
        form_layout.addRow("插件下载:", download_label)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_transform_controls(self, parent_card):
        """创建移动去重控件"""
        form_layout = ModernFormLayout()

        # 缩放比例
        self.transform_scale = ModernSpinBox()
        self.transform_scale.setRange(100, 200)
        self.transform_scale.setValue(110)
        self.transform_scale.setSuffix(" %")
        form_layout.addRow("缩放比例:", self.transform_scale)

        # 移动间隔
        self.transform_interval = ModernDoubleSpinBox()
        self.transform_interval.setRange(0.5, 120.0)
        self.transform_interval.setValue(2.0)
        self.transform_interval.setSuffix(" 秒")
        form_layout.addRow("移动间隔:", self.transform_interval)

        # 过渡时间
        self.transform_transition = ModernDoubleSpinBox()
        self.transform_transition.setRange(0.5, 120.0)
        self.transform_transition.setValue(10.0)
        self.transform_transition.setSuffix(" 秒")
        form_layout.addRow("过渡时间:", self.transform_transition)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_color_controls(self, parent_card):
        """创建颜色去重控件"""
        form_layout = ModernFormLayout()

        # 滤镜名称
        self.color_filter_name = ModernInput("自动颜色校正")
        form_layout.addRow("滤镜名称:", self.color_filter_name)

        # 色调范围
        hue_layout = QHBoxLayout()
        self.color_hue_min = ModernDoubleSpinBox()
        self.color_hue_min.setRange(-180, 180)
        self.color_hue_min.setValue(-2.0)
        self.color_hue_max = ModernDoubleSpinBox()
        self.color_hue_max.setRange(-180, 180)
        self.color_hue_max.setValue(10.0)
        hue_layout.addWidget(QLabel("最小:"))
        hue_layout.addWidget(self.color_hue_min)
        hue_layout.addWidget(QLabel("最大:"))
        hue_layout.addWidget(self.color_hue_max)
        form_layout.addRow("色调范围:", hue_layout)

        # 亮度范围
        brightness_layout = QHBoxLayout()
        self.color_brightness_min = ModernDoubleSpinBox()
        self.color_brightness_min.setRange(-1.0, 1.0)
        self.color_brightness_min.setValue(-0.05)
        self.color_brightness_min.setSingleStep(0.01)
        self.color_brightness_max = ModernDoubleSpinBox()
        self.color_brightness_max.setRange(-1.0, 1.0)
        self.color_brightness_max.setValue(0.05)
        self.color_brightness_max.setSingleStep(0.01)
        brightness_layout.addWidget(QLabel("最小:"))
        brightness_layout.addWidget(self.color_brightness_min)
        brightness_layout.addWidget(QLabel("最大:"))
        brightness_layout.addWidget(self.color_brightness_max)
        form_layout.addRow("亮度范围:", brightness_layout)

        # 变化间隔
        self.color_interval = ModernSpinBox()
        self.color_interval.setRange(100, 100000)
        self.color_interval.setValue(1500)
        self.color_interval.setSuffix(" ms")
        form_layout.addRow("变化间隔:", self.color_interval)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_audio_eq_controls(self, parent_card):
        """创建音频EQ去重控件"""
        form_layout = ModernFormLayout()

        # 滤镜名称
        self.audio_eq_filter_name = ModernInput("3段式均衡器")
        form_layout.addRow("滤镜名称:", self.audio_eq_filter_name)

        # 低频增益范围
        low_layout = QHBoxLayout()
        self.audio_low_min = ModernDoubleSpinBox()
        self.audio_low_min.setRange(-2, 2)
        self.audio_low_min.setValue(-2.0)
        self.audio_low_min.setSuffix(" dB")
        self.audio_low_max = ModernDoubleSpinBox()
        self.audio_low_max.setRange(-2, 2)
        self.audio_low_max.setValue(2.0)
        self.audio_low_max.setSuffix(" dB")
        low_layout.addWidget(QLabel("最小:"))
        low_layout.addWidget(self.audio_low_min)
        low_layout.addWidget(QLabel("最大:"))
        low_layout.addWidget(self.audio_low_max)
        form_layout.addRow("低频增益:", low_layout)

        # 中频增益范围
        mid_layout = QHBoxLayout()
        self.audio_mid_min = ModernDoubleSpinBox()
        self.audio_mid_min.setRange(-2, 2)
        self.audio_mid_min.setValue(-2.0)
        self.audio_mid_min.setSuffix(" dB")
        self.audio_mid_max = ModernDoubleSpinBox()
        self.audio_mid_max.setRange(-2, 2)
        self.audio_mid_max.setValue(2.0)
        self.audio_mid_max.setSuffix(" dB")
        mid_layout.addWidget(QLabel("最小:"))
        mid_layout.addWidget(self.audio_mid_min)
        mid_layout.addWidget(QLabel("最大:"))
        mid_layout.addWidget(self.audio_mid_max)
        form_layout.addRow("中频增益:", mid_layout)

        # 高频增益范围
        high_layout = QHBoxLayout()
        self.audio_high_min = ModernDoubleSpinBox()
        self.audio_high_min.setRange(-2, 2)
        self.audio_high_min.setValue(-2.0)
        self.audio_high_min.setSuffix(" dB")
        self.audio_high_max = ModernDoubleSpinBox()
        self.audio_high_max.setRange(-2, 2)
        self.audio_high_max.setValue(2.0)
        self.audio_high_max.setSuffix(" dB")
        high_layout.addWidget(QLabel("最小:"))
        high_layout.addWidget(self.audio_high_min)
        high_layout.addWidget(QLabel("最大:"))
        high_layout.addWidget(self.audio_high_max)
        form_layout.addRow("高频增益:", high_layout)

        # 变化间隔
        self.audio_eq_interval = ModernDoubleSpinBox()
        self.audio_eq_interval.setRange(0.1, 120.0)
        self.audio_eq_interval.setValue(1.0)
        self.audio_eq_interval.setSuffix(" s")
        form_layout.addRow("变化间隔:", self.audio_eq_interval)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_audio_mute_controls(self, parent_card):
        """创建断音控制控件"""
        form_layout = ModernFormLayout()

        # 断音间隔范围
        interval_layout = QHBoxLayout()
        self.audio_mute_interval_min = ModernDoubleSpinBox()
        self.audio_mute_interval_min.setRange(10.0, 120.0)
        self.audio_mute_interval_min.setValue(30.0)
        self.audio_mute_interval_min.setSuffix(" s")
        self.audio_mute_interval_max = ModernDoubleSpinBox()
        self.audio_mute_interval_max.setRange(10.0, 120.0)
        self.audio_mute_interval_max.setValue(40.0)
        self.audio_mute_interval_max.setSuffix(" s")
        interval_layout.addWidget(QLabel("最小:"))
        interval_layout.addWidget(self.audio_mute_interval_min)
        interval_layout.addWidget(QLabel("最大:"))
        interval_layout.addWidget(self.audio_mute_interval_max)
        form_layout.addRow("断音间隔:", interval_layout)

        # 断音持续时间范围
        duration_layout = QHBoxLayout()
        self.audio_mute_duration_min = ModernDoubleSpinBox()
        self.audio_mute_duration_min.setRange(0.1, 2.0)
        self.audio_mute_duration_min.setValue(0.1)
        self.audio_mute_duration_min.setSuffix(" s")
        self.audio_mute_duration_max = ModernDoubleSpinBox()
        self.audio_mute_duration_max.setRange(0.1, 2.0)
        self.audio_mute_duration_max.setValue(0.5)
        self.audio_mute_duration_max.setSuffix(" s")
        duration_layout.addWidget(QLabel("最短:"))
        duration_layout.addWidget(self.audio_mute_duration_min)
        duration_layout.addWidget(QLabel("最长:"))
        duration_layout.addWidget(self.audio_mute_duration_max)
        form_layout.addRow("断音时长:", duration_layout)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def create_audio_volume_controls(self, parent_card):
        """创建音量控制控件"""
        form_layout = ModernFormLayout()

        # 音量范围
        volume_layout = QHBoxLayout()
        self.audio_volume_min = ModernSpinBox()
        self.audio_volume_min.setRange(10, 100)
        self.audio_volume_min.setValue(60)
        self.audio_volume_min.setSuffix(" %")
        self.audio_volume_max = ModernSpinBox()
        self.audio_volume_max.setRange(10, 100)
        self.audio_volume_max.setValue(80)
        self.audio_volume_max.setSuffix(" %")
        volume_layout.addWidget(QLabel("最小:"))
        volume_layout.addWidget(self.audio_volume_min)
        volume_layout.addWidget(QLabel("最大:"))
        volume_layout.addWidget(self.audio_volume_max)
        form_layout.addRow("音量范围:", volume_layout)

        # 变化间隔
        interval_layout = QHBoxLayout()
        self.audio_volume_interval_min = ModernDoubleSpinBox()
        self.audio_volume_interval_min.setRange(1.0, 60.0)
        self.audio_volume_interval_min.setValue(1.0)
        self.audio_volume_interval_min.setSuffix(" s")
        self.audio_volume_interval_max = ModernDoubleSpinBox()
        self.audio_volume_interval_max.setRange(1.0, 60.0)
        self.audio_volume_interval_max.setValue(5.0)
        self.audio_volume_interval_max.setSuffix(" s")
        interval_layout.addWidget(QLabel("最小:"))
        interval_layout.addWidget(self.audio_volume_interval_min)
        interval_layout.addWidget(QLabel("最大:"))
        interval_layout.addWidget(self.audio_volume_interval_max)
        form_layout.addRow("变化间隔:", interval_layout)

        parent_card.add_config_widget(QWidget())
        parent_card.config_layout.addLayout(form_layout)

    def setup_animations(self):
        """设置动画效果"""
        # 窗口淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 启动淡入动画
        QTimer.singleShot(100, self.fade_animation.start)

    def connect_signals(self):
        """连接信号"""
        # 连接原窗口的信号到现代化界面
        if hasattr(self.original_window, 'is_connected'):
            # 监听连接状态变化
            self.connection_timer = QTimer()
            self.connection_timer.timeout.connect(self.update_connection_status)
            self.connection_timer.start(1000)  # 每秒检查一次

        # 连接媒体源变化信号
        self.video_source_combo.currentTextChanged.connect(self.on_video_source_changed)
        self.audio_source_combo.currentTextChanged.connect(self.on_audio_source_changed)

    # 信号处理方法
    def on_connect_clicked(self):
        """连接按钮点击"""
        if hasattr(self.original_window, 'connect_to_obs'):
            self.original_window.connect_to_obs()

    def on_quick_start_clicked(self):
        """一键启动按钮点击"""
        if hasattr(self.original_window, 'show_quick_start_dialog'):
            self.original_window.show_quick_start_dialog()

    def on_refresh_sources(self):
        """刷新媒体源"""
        if hasattr(self.original_window, 'refresh_media_sources'):
            self.original_window.refresh_media_sources()
            self.update_media_sources()

    def on_apply_preset(self):
        """应用预设配置"""
        preset = self.preset_combo.currentText()
        # 根据预设配置调整参数
        if preset == "轻度去重":
            self.apply_light_preset()
        elif preset == "中度去重":
            self.apply_medium_preset()
        elif preset == "重度去重":
            self.apply_heavy_preset()

    def on_download_blur_plugin(self):
        """下载模糊插件"""
        import webbrowser
        webbrowser.open("https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/iltsO2ze4wxg&pwd=eotr")

    def on_video_source_changed(self, source_name):
        """视频媒体源改变"""
        if hasattr(self.original_window, 'update_video_source_for_all_functions'):
            self.original_window.update_video_source_for_all_functions(source_name)

    def on_audio_source_changed(self, source_name):
        """音频媒体源改变"""
        if hasattr(self.original_window, 'update_audio_source_for_all_functions'):
            self.original_window.update_audio_source_for_all_functions(source_name)

    def update_connection_status(self):
        """更新连接状态"""
        if hasattr(self.original_window, 'is_connected'):
            if self.original_window.is_connected:
                self.status_indicator.update_status("connected")
                self.connect_btn.setText("🔗 已连接")
                self.connect_btn.setEnabled(False)
                self.quick_start_btn.setEnabled(True)
                self.connection_label.setText("连接状态: 已连接")
            else:
                self.status_indicator.update_status("disconnected")
                self.connect_btn.setText("🔗 连接到 OBS")
                self.connect_btn.setEnabled(True)
                self.quick_start_btn.setEnabled(False)
                self.connection_label.setText("连接状态: 未连接")

    def update_media_sources(self):
        """更新媒体源列表"""
        if hasattr(self.original_window, 'media_sources'):
            sources = self.original_window.media_sources

            # 更新视频媒体源
            self.video_source_combo.clear()
            self.video_source_combo.addItems([source.get('sourceName', '') for source in sources])

            # 更新音频媒体源
            self.audio_source_combo.clear()
            self.audio_source_combo.addItems([source.get('sourceName', '') for source in sources])

    def apply_light_preset(self):
        """应用轻度去重预设"""
        # 智能加减速
        self.speed_min_slider.setValue(95)
        self.speed_max_slider.setValue(105)

        # 模糊去重
        self.blur_min_radius.setValue(0.0)
        self.blur_max_radius.setValue(1.0)
        self.blur_interval.setValue(2000)

    def apply_medium_preset(self):
        """应用中度去重预设"""
        # 智能加减速
        self.speed_min_slider.setValue(90)
        self.speed_max_slider.setValue(120)

        # 模糊去重
        self.blur_min_radius.setValue(0.0)
        self.blur_max_radius.setValue(2.0)
        self.blur_interval.setValue(1500)

    def apply_heavy_preset(self):
        """应用重度去重预设"""
        # 智能加减速
        self.speed_min_slider.setValue(80)
        self.speed_max_slider.setValue(130)

        # 模糊去重
        self.blur_min_radius.setValue(0.0)
        self.blur_max_radius.setValue(3.0)
        self.blur_interval.setValue(1000)
