#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化UI启动脚本
简化版本，用于快速启动和测试
"""

import sys
import os

def main():
    """主函数"""
    print("🚀 启动现代化UI...")
    
    try:
        # 检查必要的文件是否存在
        required_files = [
            'modern_ui_styles.py',
            'modern_ui_components.py',
            'modern_main_window.py',
            'modern_ui_config.py',
            'main_module.py'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False
        
        print("✅ 所有必要文件存在")
        
        # 尝试导入PyQt5
        try:
            from PyQt5.QtWidgets import QApplication
            print("✅ PyQt5 导入成功")
        except ImportError as e:
            print(f"❌ PyQt5 导入失败: {e}")
            print("请确保已安装PyQt5: pip install PyQt5")
            return False
        
        # 尝试导入现代化UI模块
        try:
            from modern_ui_styles import ModernUIStyles
            from modern_ui_components import ModernCard
            from modern_ui_config import ui_config
            print("✅ 现代化UI模块导入成功")
        except Exception as e:
            print(f"❌ 现代化UI模块导入失败: {e}")
            return False
        
        # 尝试导入原始模块
        try:
            import main_module
            print("✅ 原始功能模块导入成功")
        except Exception as e:
            print(f"❌ 原始功能模块导入失败: {e}")
            return False
        
        # 启动应用程序
        app = QApplication(sys.argv)
        
        # 设置基本样式
        app.setStyleSheet(f"""
            * {{
                font-family: 'Microsoft YaHei', sans-serif;
            }}
        """)
        
        print("✅ 应用程序初始化成功")
        
        # 创建原始窗口实例
        original_window = main_module.MainWindow()
        print("✅ 原始窗口创建成功")
        
        # 尝试创建现代化窗口
        try:
            from modern_main_window import ModernMainWindow
            modern_window = ModernMainWindow(original_window)
            print("✅ 现代化窗口创建成功")
            
            # 显示现代化窗口
            modern_window.show()
            original_window.hide()
            
            print("🎉 现代化UI启动成功！")
            print("📝 使用说明:")
            print("  - 左侧面板: 媒体源配置和快速设置")
            print("  - 右侧标签页: 各种去重功能")
            print("  - 点击连接按钮连接到OBS")
            print("  - 启用需要的功能并调整参数")
            
        except Exception as e:
            print(f"⚠️ 现代化窗口创建失败，使用经典界面: {e}")
            original_window.show()
            print("✅ 经典界面启动成功")
        
        # 执行激活检查
        try:
            original_window.check_and_handle_activation()
        except Exception as e:
            print(f"⚠️ 激活检查失败: {e}")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n💡 故障排除建议:")
        print("1. 确保已安装PyQt5: pip install PyQt5")
        print("2. 检查所有文件是否完整")
        print("3. 尝试运行原版程序: python main.py")
        print("4. 查看详细错误信息")
        
        input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
