#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频设备输出修复测试工具
测试音频播放器是否能正确输出到指定声卡
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QPushButton, QComboBox, QTextEdit,
                             QGroupBox, QProgressBar, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat
import numpy as np
import tempfile

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False

class AudioDeviceFixTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 音频设备输出修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 音频相关变量
        self.current_audio_output = None
        self.selected_audio_device = None
        self.audio_buffers = []
        
        self.init_ui()
        self.load_audio_devices()
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎯 音频设备输出修复验证")
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 修复说明
        fix_info = QLabel("""
        ✅ 已实施的修复方案：
        
        1. 🔧 添加了 play_audio_through_device() 方法，直接通过QAudioOutput播放音频流
        2. 📊 修改了 play_audio_with_effects() 方法，优先使用指定设备
        3. 🎯 添加了 play_basic_audio_file() 方法，确保无特效播放也使用指定设备
        4. 🔄 改进了设备设置逻辑，避免提前创建音频输出对象
        5. 🧹 添加了音频缓冲区清理机制
        """)
        fix_info.setStyleSheet("""
            QLabel {
                background: #e8f5e8;
                border: 1px solid #4caf50;
                border-radius: 8px;
                padding: 15px;
                color: #2e7d32;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(fix_info)
        
        # 设备选择区域
        device_group = QGroupBox("🔊 音频输出设备选择")
        device_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        device_layout = QVBoxLayout(device_group)
        
        # 设备选择下拉框
        device_select_layout = QHBoxLayout()
        device_select_layout.addWidget(QLabel("选择音频设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        device_select_layout.addWidget(self.device_combo)
        
        device_select_layout.addStretch()
        device_layout.addLayout(device_select_layout)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        
        self.test_tone_btn = QPushButton("🎵 测试音调")
        self.test_tone_btn.clicked.connect(self.test_audio_tone)
        self.test_tone_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        """)
        test_layout.addWidget(self.test_tone_btn)
        
        self.test_noise_btn = QPushButton("🔊 测试白噪声")
        self.test_noise_btn.clicked.connect(self.test_white_noise)
        self.test_noise_btn.setStyleSheet(self.test_tone_btn.styleSheet())
        test_layout.addWidget(self.test_noise_btn)
        
        test_layout.addStretch()
        device_layout.addLayout(test_layout)
        
        layout.addWidget(device_group)
        
        # 状态显示区域
        status_group = QGroupBox("📊 测试状态")
        status_group.setStyleSheet(device_group.styleSheet())
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
    def load_audio_devices(self):
        """加载音频输出设备"""
        try:
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            self.device_combo.clear()
            self.device_combo.addItem("系统默认设备", None)
            
            for device in devices:
                device_name = device.deviceName()
                self.device_combo.addItem(f"🎵 {device_name}", device)
                self.log(f"发现音频设备: {device_name}")
            
            self.log(f"总共找到 {len(devices)} 个音频输出设备")
            
        except Exception as e:
            self.log(f"❌ 加载音频设备时出错: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        selected_device = self.device_combo.currentData()
        
        if selected_device:
            self.selected_audio_device = selected_device
            self.log(f"✅ 已选择设备: {device_name}")
        else:
            self.selected_audio_device = None
            self.log(f"ℹ️ 已选择系统默认设备")
    
    def test_audio_tone(self):
        """测试音调播放"""
        if not AUDIO_PROCESSING_AVAILABLE:
            QMessageBox.warning(self, "警告", "音频处理库不可用，无法生成测试音调")
            return
            
        try:
            self.log("🎵 开始生成测试音调...")
            
            # 生成440Hz正弦波（A4音符）
            duration = 2.0  # 2秒
            sample_rate = 44100
            frequency = 440.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            # 转为立体声
            audio_data = np.column_stack((audio_data, audio_data))
            
            # 播放音频
            success = self.play_audio_through_device(audio_data, sample_rate)
            
            if success:
                self.log("✅ 测试音调播放成功")
            else:
                self.log("❌ 测试音调播放失败")
                
        except Exception as e:
            self.log(f"❌ 生成测试音调时出错: {e}")
    
    def test_white_noise(self):
        """测试白噪声播放"""
        if not AUDIO_PROCESSING_AVAILABLE:
            QMessageBox.warning(self, "警告", "音频处理库不可用，无法生成白噪声")
            return
            
        try:
            self.log("🔊 开始生成白噪声...")
            
            # 生成白噪声
            duration = 1.0  # 1秒
            sample_rate = 44100
            
            audio_data = 0.1 * np.random.normal(0, 1, int(sample_rate * duration))
            
            # 转为立体声
            audio_data = np.column_stack((audio_data, audio_data))
            
            # 播放音频
            success = self.play_audio_through_device(audio_data, sample_rate)
            
            if success:
                self.log("✅ 白噪声播放成功")
            else:
                self.log("❌ 白噪声播放失败")
                
        except Exception as e:
            self.log(f"❌ 生成白噪声时出错: {e}")
    
    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频（修复后的方法）"""
        try:
            if not self.selected_audio_device:
                self.log("⚠️ 使用系统默认设备播放")
                return False
            
            self.log(f"🎯 尝试通过设备播放: {self.selected_audio_device.deviceName()}")
            
            # 转换为16位整数格式
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            
            # 创建音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(audio_data_int16.shape[1])
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            # 检查设备支持
            if not self.selected_audio_device.isFormatSupported(format):
                self.log("⚠️ 设备不支持指定格式，使用最接近格式")
                format = self.selected_audio_device.nearestFormat(format)
            
            # 创建音频输出
            if self.current_audio_output:
                self.current_audio_output.stop()
            
            self.current_audio_output = QAudioOutput(self.selected_audio_device, format)
            self.current_audio_output.setVolume(0.5)
            
            # 创建音频数据缓冲区
            from PyQt5.QtCore import QBuffer
            buffer = QBuffer()
            buffer.setData(audio_data_int16.tobytes())
            buffer.open(buffer.ReadOnly)
            
            # 开始播放
            self.current_audio_output.start(buffer)
            
            self.log(f"✅ 开始播放 - 设备: {self.selected_audio_device.deviceName()}")
            self.log(f"  格式: {format.sampleRate()}Hz, {format.channelCount()}声道")
            
            # 保存缓冲区引用
            self.audio_buffers.append(buffer)
            
            return True
            
        except Exception as e:
            self.log(f"❌ 播放失败: {e}")
            return False
    
    def log(self, message):
        """添加日志消息"""
        self.status_text.append(f"[{QTimer().remainingTime()}] {message}")
        self.status_text.ensureCursorVisible()

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background: #f8fafc;
        }
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }
    """)
    
    window = AudioDeviceFixTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
