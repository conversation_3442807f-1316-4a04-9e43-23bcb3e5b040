#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
碎片模式测试 - 验证正常模式和碎片模式的区别
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QPolygon, QPoint

class FragmentModeTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🔪 碎片模式测试')
        self.setGeometry(300, 300, 800, 600)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("🔪 碎片模式功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 功能说明
        info = QLabel("""
        🎯 碎片模式功能说明：
        
        📐 正常模式（未开启"自由无视间隔剪切"）：
        • 显示完整的长方形
        • 按照轮播设置的时间间隔切换颜色（500-2000ms）
        • 形状保持完整和规整
        
        🔪 碎片模式（开启"自由无视间隔剪切"）：
        • 将长方形"剪切"成不规则碎片
        • 按照碎片数据范围的时间间隔快速切换（10-30ms）
        • 产生破碎、分裂的视觉效果
        """)
        info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        
        # 正常模式测试
        normal_btn = QPushButton("📐 测试正常模式")
        normal_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        normal_btn.clicked.connect(lambda: self.test_mode(False))
        
        # 碎片模式测试
        fragment_btn = QPushButton("🔪 测试碎片模式")
        fragment_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        fragment_btn.clicked.connect(lambda: self.test_mode(True))
        
        button_layout.addWidget(normal_btn)
        button_layout.addWidget(fragment_btn)
        layout.addLayout(button_layout)
        
        # 主程序测试按钮
        main_test_btn = QPushButton("🚀 测试主程序完整功能")
        main_test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        main_test_btn.clicked.connect(self.test_main_program)
        layout.addWidget(main_test_btn)
        
        # 结果显示
        self.result_label = QLabel("点击按钮测试不同模式的视觉效果")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
        
    def test_mode(self, is_fragment_mode):
        """测试指定模式"""
        # 创建测试窗口
        self.test_window = QWidget()
        mode_name = "碎片模式" if is_fragment_mode else "正常模式"
        self.test_window.setWindowTitle(f"{mode_name}测试")
        self.test_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # 设置9:16比例
        window_height = 600
        window_width = int(window_height * 9 / 16)
        self.test_window.resize(window_width, window_height)
        
        # 居中显示
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - window_width) // 2
            y = (screen_geometry.height() - window_height) // 2
            self.test_window.move(x, y)
        
        # 设置窗口属性
        self.test_window.current_color = "#ff0000"
        self.test_window.is_fragment_mode = is_fragment_mode
        self.test_window.drag_position = None
        
        # 绘制方法
        def paintEvent(event):
            try:
                painter = QPainter(self.test_window)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 背景
                painter.fillRect(self.test_window.rect(), QColor("#f0f0f0"))
                
                # 设置颜色
                color = QColor(self.test_window.current_color)
                painter.setBrush(QBrush(color))
                painter.setPen(QPen(color.darker(120), 3))
                
                width = self.test_window.width()
                height = self.test_window.height()
                
                if self.test_window.is_fragment_mode:
                    # 碎片模式：绘制被剪切的碎片
                    import random
                    random.seed(42)  # 固定种子确保一致性
                    
                    # 基础长方形区域
                    base_rect_width = width * 0.8
                    base_rect_height = height * 0.6
                    base_x = (width - base_rect_width) / 2
                    base_y = (height - base_rect_height) / 2
                    
                    # 生成碎片
                    fragment_count = 12
                    for i in range(fragment_count):
                        frag_x = base_x + random.uniform(0, base_rect_width * 0.8)
                        frag_y = base_y + random.uniform(0, base_rect_height * 0.8)
                        frag_w = random.uniform(25, 60)
                        frag_h = random.uniform(25, 60)
                        
                        frag_x = max(10, min(width - frag_w - 10, frag_x))
                        frag_y = max(10, min(height - frag_h - 10, frag_y))
                        
                        if i % 3 == 0:
                            painter.drawRect(int(frag_x), int(frag_y), int(frag_w), int(frag_h))
                        elif i % 3 == 1:
                            painter.drawEllipse(int(frag_x), int(frag_y), int(frag_w), int(frag_h))
                        else:
                            # 不规则多边形
                            points = []
                            center_x = frag_x + frag_w / 2
                            center_y = frag_y + frag_h / 2
                            for j in range(5):
                                angle = (2 * 3.14159 * j) / 5
                                radius = min(frag_w, frag_h) / 2 * random.uniform(0.6, 1.0)
                                px = center_x + radius * random.uniform(0.8, 1.2) * (1 if j % 2 == 0 else -1)
                                py = center_y + radius * random.uniform(0.8, 1.2) * (1 if j % 2 == 0 else -1)
                                points.append(QPoint(int(px), int(py)))
                            
                            if len(points) >= 3:
                                painter.drawPolygon(QPolygon(points))
                else:
                    # 正常模式：完整长方形
                    rect_width = width * 0.7
                    rect_height = height * 0.5
                    rect_x = (width - rect_width) / 2
                    rect_y = (height - rect_height) / 2
                    
                    painter.drawRect(int(rect_x), int(rect_y), int(rect_width), int(rect_height))
                    
                    # 装饰性小长方形
                    for i in range(3):
                        small_w = width * 0.12
                        small_h = height * 0.08
                        small_x = width * 0.15 + i * width * 0.25
                        small_y = height * 0.8
                        painter.drawRect(int(small_x), int(small_y), int(small_w), int(small_h))
                
                # 提示文字
                painter.setPen(QPen(QColor("white"), 2))
                painter.drawText(20, 30, f"模式: {mode_name}")
                painter.drawText(20, 50, "右键或ESC关闭")
                
            except Exception as e:
                print(f"绘制错误: {e}")
        
        # 事件处理
        def keyPressEvent(event):
            if event.key() == Qt.Key_Escape:
                self.test_window.close()
        
        def mousePressEvent(event):
            if event.button() == Qt.RightButton:
                self.test_window.close()
        
        # 绑定事件
        self.test_window.paintEvent = paintEvent
        self.test_window.keyPressEvent = keyPressEvent
        self.test_window.mousePressEvent = mousePressEvent
        
        # 显示窗口
        self.test_window.show()
        self.test_window.setFocus()
        self.test_window.raise_()
        
        # 更新结果显示
        if is_fragment_mode:
            self.result_label.setText("🔪 碎片模式测试窗口已打开\n显示被剪切的不规则碎片效果")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #fee2e2;
                    border: 1px solid #dc2626;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #dc2626;
                    min-height: 60px;
                }
            """)
        else:
            self.result_label.setText("📐 正常模式测试窗口已打开\n显示完整的长方形效果")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dcfce7;
                    border: 1px solid #16a34a;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #15803d;
                    min-height: 60px;
                }
            """)
        
        print(f"✅ {mode_name}测试窗口已创建")
        
    def test_main_program(self):
        """测试主程序"""
        try:
            from main_module import OBSControlApp
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            main_window = OBSControlApp()
            main_window.show()
            
            # 切换到爆闪播放器标签页
            tab_widget = main_window.tab_widget
            for i in range(tab_widget.count()):
                if "爆闪播放器" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
            
            self.result_label.setText("✅ 主程序已启动！\n请测试正常模式和碎片模式的区别")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dbeafe;
                    border: 1px solid #3b82f6;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #1d4ed8;
                    min-height: 60px;
                }
            """)
            
        except Exception as e:
            self.result_label.setText(f"❌ 启动失败：{e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FragmentModeTest()
    window.show()
    sys.exit(app.exec_())
