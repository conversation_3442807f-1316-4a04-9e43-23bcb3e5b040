#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的UI测试程序
用于验证现代化UI的基本功能
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试PyQt5
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 导入成功")
        
        # 测试现代化UI模块
        from modern_ui_styles import ModernUIStyles
        print("✅ 样式模块导入成功")
        
        from modern_ui_components import ModernCard, ModernButton
        print("✅ 组件模块导入成功")
        
        from modern_ui_config import ui_config
        print("✅ 配置模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_ui():
    """测试基本UI"""
    print("\n🎨 测试基本UI组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
        from modern_ui_styles import ModernUIStyles
        from modern_ui_components import ModernCard, ModernButton
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QWidget()
        window.setWindowTitle("现代化UI测试")
        window.setGeometry(100, 100, 400, 300)
        
        # 应用样式
        window.setStyleSheet(ModernUIStyles.get_main_window_style())
        
        # 创建布局
        layout = QVBoxLayout(window)
        
        # 添加标题
        title = QLabel("🎉 现代化UI测试成功！")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 16pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                text-align: center;
                padding: 20px;
            }}
        """)
        layout.addWidget(title)
        
        # 添加卡片
        card = ModernCard("测试卡片", "这是一个现代化UI卡片组件")
        layout.addWidget(card)
        
        # 添加按钮
        button = ModernButton("测试按钮", "primary")
        button.clicked.connect(lambda: print("按钮被点击！"))
        layout.addWidget(button)
        
        # 显示窗口
        window.show()
        
        print("✅ UI组件创建成功")
        print("📝 窗口已显示，请查看效果")
        print("💡 关闭窗口以继续...")
        
        # 运行应用程序
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 现代化UI简单测试")
    print("=" * 40)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，无法继续")
        return False
    
    print("\n✅ 所有模块导入成功！")
    
    # 询问是否继续UI测试
    try:
        choice = input("\n是否继续UI界面测试？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            return test_basic_ui()
        else:
            print("跳过UI测试")
            return True
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        return False

if __name__ == '__main__':
    try:
        success = main()
        
        if success:
            print("\n🎉 测试完成！")
            print("\n📋 下一步:")
            print("1. 运行 'python start_modern_ui.py' 启动完整的现代化界面")
            print("2. 运行 'python modern_main.py' 使用完整功能")
            print("3. 运行 'python main.py' 使用原版界面")
        else:
            print("\n❌ 测试失败")
            print("\n💡 故障排除:")
            print("1. 确保安装了PyQt5: pip install PyQt5")
            print("2. 检查所有现代化UI文件是否存在")
            print("3. 查看上面的错误信息")
            
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
