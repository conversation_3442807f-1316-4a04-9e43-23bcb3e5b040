#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UI预览 - 显示更新后的爆闪播放器界面
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox, QSpinBox, QFormLayout, QCheckBox
)
from PyQt5.QtCore import Qt

class UIPreview(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🎨 爆闪播放器UI预览')
        self.setGeometry(200, 200, 1000, 700)
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("⚡ 爆闪播放器")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # 说明文字
        desc = QLabel("✨ 这个播放器会生成随机的几何形状，并根据您选择的颜色进行无规则变化")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("color: #64748b; font-size: 12pt; padding: 10px;")
        main_layout.addWidget(desc)
        
        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        content_layout.addWidget(left_panel, 1)
        
        # 右侧面板 - 重点展示碎片数据范围
        right_panel = self.create_right_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        start_btn = QPushButton("🚀 启动几何形状轮播")
        start_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        
        stop_btn = QPushButton("⏹️ 停止轮播")
        stop_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(start_btn)
        button_layout.addWidget(stop_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
    def create_left_panel(self):
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 颜色管理
        color_group = QGroupBox("🎨 颜色管理")
        color_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        color_layout = QVBoxLayout(color_group)
        
        # 模拟按钮
        btn_layout = QHBoxLayout()
        for text in ["🎨 选择颜色", "➕ 添加颜色", "🗑️ 清空颜色"]:
            btn = QPushButton(text)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 12px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 10pt;
                    background: #f1f5f9;
                    border: 1px solid #cbd5e1;
                }
            """)
            btn_layout.addWidget(btn)
        color_layout.addLayout(btn_layout)
        
        # 当前颜色显示
        current_color = QLabel("当前颜色: 未选择")
        current_color.setStyleSheet("""
            QLabel {
                padding: 8px;
                background: #f1f5f9;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                font-weight: bold;
            }
        """)
        color_layout.addWidget(current_color)
        
        layout.addWidget(color_group)
        
        # 轮播模式
        mode_group = QGroupBox("🔄 轮播模式")
        mode_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        mode_layout = QVBoxLayout(mode_group)
        
        mode1 = QLabel("○ 顺序轮播")
        mode2 = QLabel("○ 随机乱序轮播")
        mode_layout.addWidget(mode1)
        mode_layout.addWidget(mode2)
        
        layout.addWidget(mode_group)
        
        return panel
        
    def create_right_panel(self):
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 无视间隔设置
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)
        
        # 复选框
        checkbox = QCheckBox("自由无视间隔剪切")
        checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                font-size: 12pt;
            }
        """)
        interval_layout.addWidget(checkbox)
        
        # 碎片数据范围标题 - 突出显示
        fragment_title = QLabel("📊 碎片数据范围")
        fragment_title.setAlignment(Qt.AlignCenter)
        fragment_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #dc2626;
                font-size: 13pt;
                margin: 8px 0px;
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                padding: 8px;
                border-radius: 8px;
                border: 2px solid #dc2626;
            }
        """)
        interval_layout.addWidget(fragment_title)
        
        # 碎片设置容器 - 黄色背景突出显示
        fragment_container = QWidget()
        fragment_container.setStyleSheet("""
            QWidget {
                background: linear-gradient(135deg, #fef9c3 0%, #fef08a 100%);
                border: 3px solid #eab308;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)
        fragment_layout = QFormLayout(fragment_container)
        fragment_layout.setSpacing(12)
        
        # 最小值
        min_label = QLabel("最小:")
        min_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 12pt;
            }
        """)
        min_spin = QSpinBox()
        min_spin.setValue(10)
        min_spin.setStyleSheet("""
            QSpinBox {
                padding: 12px;
                border: 3px solid #d97706;
                border-radius: 8px;
                background: white;
                font-size: 14pt;
                font-weight: bold;
                min-width: 120px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(min_label, min_spin)
        
        # 最大值
        max_label = QLabel("最大:")
        max_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 12pt;
            }
        """)
        max_spin = QSpinBox()
        max_spin.setValue(30)
        max_spin.setStyleSheet("""
            QSpinBox {
                padding: 12px;
                border: 3px solid #d97706;
                border-radius: 8px;
                background: white;
                font-size: 14pt;
                font-weight: bold;
                min-width: 120px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(max_label, max_spin)
        
        # 说明文字
        fragment_info = QLabel("💡 启用碎片模式后，将使用上述数值范围进行超快速切换（毫秒）")
        fragment_info.setWordWrap(True)
        fragment_info.setStyleSheet("""
            QLabel {
                color: #92400e;
                font-size: 10pt;
                font-style: italic;
                margin-top: 8px;
                padding: 5px;
                background: rgba(255, 255, 255, 0.7);
                border-radius: 4px;
            }
        """)
        fragment_layout.addRow(fragment_info)
        
        interval_layout.addWidget(fragment_container)
        layout.addWidget(interval_group)
        
        return panel

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = UIPreview()
    window.show()
    sys.exit(app.exec_())
