#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QGroupBox, QListWidget, QSpinBox,
    QCheckBox, QRadioButton, QColorDialog, QMessageBox, QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

class FlashPlayerTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('爆闪播放器测试')
        self.setGeometry(300, 300, 800, 600)
        
        # 初始化状态
        self.flash_player_state = {
            "colors": [],
            "is_playing": False,
            "play_mode": "顺序轮播",
            "ignore_interval": False,
            "min_interval": 500,
            "max_interval": 2000,
            "current_color_index": 0,
            "flash_window": None,
            "flash_timer": QTimer(self)
        }
        
        self.selected_color = None
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        
        # 标题
        title = QLabel("⚡ 爆闪播放器测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 内容区域
        content_layout = QHBoxLayout()
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        content_layout.addWidget(left_panel)
        
        # 右侧显示面板
        right_panel = self.create_display_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🚀 启动轮播")
        self.start_btn.clicked.connect(self.start_flash)
        self.stop_btn = QPushButton("⏹️ 停止轮播")
        self.stop_btn.clicked.connect(self.stop_flash)
        self.stop_btn.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
    def create_control_panel(self):
        panel = QWidget()
        panel.setStyleSheet("background: white; border: 2px solid #e2e8f0; border-radius: 12px;")
        layout = QVBoxLayout(panel)
        
        # 颜色管理
        color_group = QGroupBox("🎨 颜色管理")
        color_layout = QVBoxLayout(color_group)
        
        color_btn_layout = QHBoxLayout()
        self.pick_color_btn = QPushButton("🎨 选择颜色")
        self.pick_color_btn.clicked.connect(self.pick_color)
        self.add_color_btn = QPushButton("➕ 添加颜色")
        self.add_color_btn.clicked.connect(self.add_color)
        self.clear_colors_btn = QPushButton("🗑️ 清空颜色")
        self.clear_colors_btn.clicked.connect(self.clear_colors)
        
        color_btn_layout.addWidget(self.pick_color_btn)
        color_btn_layout.addWidget(self.add_color_btn)
        color_btn_layout.addWidget(self.clear_colors_btn)
        color_layout.addLayout(color_btn_layout)
        
        self.current_color_display = QLabel("当前颜色: 未选择")
        color_layout.addWidget(self.current_color_display)
        
        layout.addWidget(color_group)
        
        # 播放模式
        mode_group = QGroupBox("🔄 轮播模式")
        mode_layout = QVBoxLayout(mode_group)
        
        self.mode_sequential = QRadioButton("顺序轮播")
        self.mode_sequential.setChecked(True)
        self.mode_random = QRadioButton("随机乱序轮播")
        
        mode_layout.addWidget(self.mode_sequential)
        mode_layout.addWidget(self.mode_random)
        
        layout.addWidget(mode_group)
        
        return panel
        
    def create_display_panel(self):
        panel = QWidget()
        panel.setStyleSheet("background: white; border: 2px solid #e2e8f0; border-radius: 12px;")
        layout = QVBoxLayout(panel)
        
        # 间隔设置
        interval_group = QGroupBox("⏱️ 间隔设置")
        interval_layout = QVBoxLayout(interval_group)
        
        self.ignore_interval = QCheckBox("自由无视间隔剪切")
        interval_layout.addWidget(self.ignore_interval)
        
        layout.addWidget(interval_group)
        
        # 时间设置
        time_group = QGroupBox("🎛️ 轮播设置【毫秒】")
        time_layout = QVBoxLayout(time_group)
        
        min_layout = QHBoxLayout()
        min_layout.addWidget(QLabel("最小:"))
        self.min_spin = QSpinBox()
        self.min_spin.setRange(10, 10000)
        self.min_spin.setValue(500)
        self.min_spin.setSuffix(" ms")
        min_layout.addWidget(self.min_spin)
        time_layout.addLayout(min_layout)
        
        max_layout = QHBoxLayout()
        max_layout.addWidget(QLabel("最大:"))
        self.max_spin = QSpinBox()
        self.max_spin.setRange(10, 10000)
        self.max_spin.setValue(2000)
        self.max_spin.setSuffix(" ms")
        max_layout.addWidget(self.max_spin)
        time_layout.addLayout(max_layout)
        
        layout.addWidget(time_group)
        
        # 颜色列表
        colors_group = QGroupBox("🎨 已添加颜色")
        colors_layout = QVBoxLayout(colors_group)
        
        self.colors_list = QListWidget()
        colors_layout.addWidget(self.colors_list)
        
        layout.addWidget(colors_group)
        
        return panel
        
    def pick_color(self):
        color = QColorDialog.getColor()
        if color.isValid():
            self.selected_color = color
            color_name = color.name()
            self.current_color_display.setText(f"当前颜色: {color_name}")
            self.current_color_display.setStyleSheet(f"""
                QLabel {{
                    padding: 8px;
                    background: {color_name};
                    border-radius: 6px;
                    color: {'white' if color.lightness() < 128 else 'black'};
                }}
            """)
            
    def add_color(self):
        if hasattr(self, 'selected_color') and self.selected_color and self.selected_color.isValid():
            color_name = self.selected_color.name()
            if color_name not in self.flash_player_state["colors"]:
                self.flash_player_state["colors"].append(color_name)
                self.update_colors_list()
                print(f"添加颜色: {color_name}")
            else:
                QMessageBox.information(self, "提示", "该颜色已存在！")
        else:
            QMessageBox.warning(self, "警告", "请先选择一个颜色！")
            
    def clear_colors(self):
        reply = QMessageBox.question(self, "确认", "确定要清空所有颜色吗？")
        if reply == QMessageBox.Yes:
            self.flash_player_state["colors"].clear()
            self.update_colors_list()
            
    def update_colors_list(self):
        self.colors_list.clear()
        for color in self.flash_player_state["colors"]:
            item = QListWidgetItem(f"🎨 {color}")
            self.colors_list.addItem(item)
            
    def start_flash(self):
        if not self.flash_player_state["colors"]:
            QMessageBox.warning(self, "警告", "请先添加至少一个颜色！")
            return
            
        self.create_flash_window()
        self.flash_player_state["is_playing"] = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 开始播放
        self.flash_next_color()
        
    def stop_flash(self):
        self.flash_player_state["is_playing"] = False
        self.flash_player_state["flash_timer"].stop()
        
        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].close()
            self.flash_player_state["flash_window"] = None
            
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
    def create_flash_window(self):
        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].close()
            
        flash_window = QWidget()
        flash_window.setWindowTitle("爆闪播放器")
        flash_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        flash_window.showFullScreen()
        flash_window.setStyleSheet("background-color: black;")
        
        def keyPressEvent(event):
            if event.key() == Qt.Key_Escape:
                self.stop_flash()
                
        flash_window.keyPressEvent = keyPressEvent
        flash_window.setFocusPolicy(Qt.StrongFocus)
        
        self.flash_player_state["flash_window"] = flash_window
        
    def flash_next_color(self):
        if not self.flash_player_state["is_playing"] or not self.flash_player_state["colors"]:
            return
            
        colors = self.flash_player_state["colors"]
        
        if self.mode_sequential.isChecked():
            color = colors[self.flash_player_state["current_color_index"]]
            self.flash_player_state["current_color_index"] = (self.flash_player_state["current_color_index"] + 1) % len(colors)
        else:
            import random
            color = random.choice(colors)
            
        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].setStyleSheet(f"background-color: {color};")
            
        # 设置下次切换时间
        if not self.ignore_interval.isChecked():
            import random
            interval = random.randint(self.min_spin.value(), self.max_spin.value())
            self.flash_player_state["flash_timer"].timeout.connect(self.flash_next_color)
            self.flash_player_state["flash_timer"].start(interval)
        else:
            self.flash_player_state["flash_timer"].timeout.connect(self.flash_next_color)
            self.flash_player_state["flash_timer"].start(50)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FlashPlayerTest()
    window.show()
    sys.exit(app.exec_())
