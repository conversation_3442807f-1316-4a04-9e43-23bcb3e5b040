# -*- coding: utf-8 -*-
"""
现代化UI测试程序
用于测试现代化UI组件和样式
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modern_ui_styles import ModernUIStyles
from modern_ui_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernCheckBox, ModernSlider, StatusIndicator, FeatureCard,
    ModernSpinBox, ModernDoubleSpinBox, ModernProgressBar
)

class ModernUITestWindow(QWidget):
    """现代化UI测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_test_ui()
    
    def setup_window(self):
        """设置窗口"""
        self.setWindowTitle("现代化UI组件测试")
        self.setGeometry(100, 100, 800, 600)
        self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_test_ui(self):
        """创建测试UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_card = ModernCard("🧪 现代化UI组件测试", "测试各种现代化UI组件的显示效果")
        main_layout.addWidget(title_card)
        
        # 按钮测试
        button_card = ModernCard("按钮组件测试")
        button_layout = QHBoxLayout()
        
        primary_btn = ModernButton("主要按钮", "primary")
        success_btn = ModernButton("成功按钮", "success")
        disabled_btn = ModernButton("禁用按钮")
        disabled_btn.setEnabled(False)
        
        button_layout.addWidget(primary_btn)
        button_layout.addWidget(success_btn)
        button_layout.addWidget(disabled_btn)
        button_card.add_layout(button_layout)
        main_layout.addWidget(button_card)
        
        # 输入组件测试
        input_card = ModernCard("输入组件测试")
        input_layout = QVBoxLayout()
        
        # 文本输入
        text_input = ModernInput("请输入文本...")
        input_layout.addWidget(text_input)
        
        # 下拉框
        combo_box = ModernComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3"])
        input_layout.addWidget(combo_box)
        
        # 复选框
        checkbox = ModernCheckBox("启用此选项")
        input_layout.addWidget(checkbox)
        
        # 数字输入框
        number_layout = QHBoxLayout()
        spin_box = ModernSpinBox()
        spin_box.setRange(0, 100)
        spin_box.setValue(50)
        double_spin_box = ModernDoubleSpinBox()
        double_spin_box.setRange(0.0, 10.0)
        double_spin_box.setValue(5.0)
        double_spin_box.setSingleStep(0.1)
        number_layout.addWidget(spin_box)
        number_layout.addWidget(double_spin_box)
        input_layout.addLayout(number_layout)
        
        input_card.add_layout(input_layout)
        main_layout.addWidget(input_card)
        
        # 滑块和进度条测试
        slider_card = ModernCard("滑块和进度条测试")
        slider_layout = QVBoxLayout()
        
        # 滑块
        slider = ModernSlider()
        slider.setRange(0, 100)
        slider.setValue(50)
        slider_layout.addWidget(slider)
        
        # 进度条
        progress_bar = ModernProgressBar()
        progress_bar.setRange(0, 100)
        progress_bar.setValue(75)
        slider_layout.addWidget(progress_bar)
        
        slider_card.add_layout(slider_layout)
        main_layout.addWidget(slider_card)
        
        # 状态指示器测试
        status_card = ModernCard("状态指示器测试")
        status_layout = QHBoxLayout()
        
        connected_status = StatusIndicator("connected")
        connecting_status = StatusIndicator("connecting")
        disconnected_status = StatusIndicator("disconnected")
        error_status = StatusIndicator("error")
        
        status_layout.addWidget(connected_status)
        status_layout.addWidget(connecting_status)
        status_layout.addWidget(disconnected_status)
        status_layout.addWidget(error_status)
        
        status_card.add_layout(status_layout)
        main_layout.addWidget(status_card)
        
        # 功能卡片测试
        feature_card = FeatureCard(
            "测试功能",
            "这是一个功能卡片的测试，包含启用开关和配置区域",
            "🧪"
        )
        
        # 添加一些配置控件到功能卡片
        test_input = ModernInput("功能配置参数")
        test_slider = ModernSlider()
        test_slider.setRange(0, 100)
        test_slider.setValue(30)
        
        feature_card.add_config_widget(test_input)
        feature_card.add_config_widget(test_slider)
        
        main_layout.addWidget(feature_card)
        
        # 连接信号测试
        self.connect_test_signals(primary_btn, success_btn, checkbox, slider, progress_bar)
    
    def connect_test_signals(self, primary_btn, success_btn, checkbox, slider, progress_bar):
        """连接测试信号"""
        # 按钮点击测试
        primary_btn.clicked.connect(lambda: print("主要按钮被点击"))
        success_btn.clicked.connect(lambda: print("成功按钮被点击"))
        
        # 复选框状态改变
        checkbox.stateChanged.connect(lambda state: print(f"复选框状态: {'选中' if state == Qt.Checked else '未选中'}"))
        
        # 滑块值改变
        slider.valueChanged.connect(lambda value: (
            print(f"滑块值: {value}"),
            progress_bar.setValue(value)
        ))

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyleSheet(f"""
        * {{
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }}
        
        QApplication {{
            background: {ModernUIStyles.COLORS['background']};
        }}
    """)
    
    # 创建测试窗口
    test_window = ModernUITestWindow()
    test_window.show()
    
    print("现代化UI测试程序启动成功")
    print("测试各种组件的交互效果...")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
