#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频输出设备测试 - 验证音频是否输出到指定设备
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QComboBox, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio

class AudioOutputTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🔊 音频输出设备测试')
        self.setGeometry(300, 300, 700, 500)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("🔊 音频输出设备测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 问题说明
        problem_info = QLabel("""
        ❌ 当前问题：
        • 在音频播放器中选择了输出设备
        • 但音频仍然从默认设备输出
        • 没有切换到指定的音频设备
        """)
        problem_info.setStyleSheet("""
            QLabel {
                background: #fee2e2;
                border: 1px solid #dc2626;
                border-radius: 8px;
                padding: 15px;
                color: #dc2626;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(problem_info)
        
        # 解决方案说明
        solution_info = QLabel("""
        ✅ 已实施的修复：
        
        1. 🔧 修复了QMediaPlayer与音频输出设备的关联
        2. 📊 添加了音频输出设备状态的详细日志
        3. 🎯 确保音频效果处理后使用指定设备播放
        4. 🔄 改进了设备设置的错误处理机制
        
        💡 注意事项：
        • Qt 5.x版本中QMediaPlayer对音频设备的支持有限
        • VoiceMeeter等虚拟音频设备可能需要特殊处理
        • 建议检查系统音频设置和设备驱动
        """)
        solution_info.setStyleSheet("""
            QLabel {
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 8px;
                padding: 15px;
                color: #0c4a6e;
                font-size: 11pt;
                line-height: 1.5;
            }
        """)
        layout.addWidget(solution_info)
        
        # 可用音频设备列表
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("可用音频输出设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
                min-width: 300px;
            }
        """)
        self.load_audio_devices()
        device_layout.addWidget(self.device_combo)
        
        layout.addLayout(device_layout)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        test_layout.setSpacing(15)
        
        # 测试当前设备
        test_current_btn = QPushButton("🔊 测试当前选择的设备")
        test_current_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        test_current_btn.clicked.connect(self.test_current_device)
        
        # 测试主程序
        test_main_btn = QPushButton("🚀 测试主程序音频功能")
        test_main_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
        """)
        test_main_btn.clicked.connect(self.test_main_program)
        
        test_layout.addWidget(test_current_btn)
        test_layout.addWidget(test_main_btn)
        layout.addLayout(test_layout)
        
        # 调试建议
        debug_info = QLabel("""
        🔍 调试建议：
        
        1. 📋 检查控制台输出中的音频设备信息
        2. 🎵 播放音频时观察日志中的设备名称
        3. 🔧 确认VoiceMeeter等虚拟设备正常运行
        4. 🎚️ 检查Windows音频设置中的默认播放设备
        5. 🔄 尝试重启程序或重新选择音频设备
        """)
        debug_info.setStyleSheet("""
            QLabel {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                padding: 15px;
                color: #92400e;
                font-size: 10pt;
                line-height: 1.4;
            }
        """)
        layout.addWidget(debug_info)
        
        # 结果显示
        self.result_label = QLabel("选择音频设备并点击测试按钮")
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 15px;
                font-size: 11pt;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
        
    def load_audio_devices(self):
        """加载可用的音频输出设备"""
        try:
            # 获取所有可用的音频输出设备
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            self.device_combo.clear()
            self.device_combo.addItem("系统默认设备", None)
            
            for device in devices:
                device_name = device.deviceName()
                self.device_combo.addItem(device_name, device)
                print(f"发现音频设备: {device_name}")
            
            print(f"总共找到 {len(devices)} 个音频输出设备")
            
        except Exception as e:
            print(f"加载音频设备时出错: {e}")
            self.device_combo.addItem("无法加载设备", None)
    
    def test_current_device(self):
        """测试当前选择的音频设备"""
        try:
            selected_device = self.device_combo.currentData()
            device_name = self.device_combo.currentText()
            
            if selected_device:
                print(f"🔊 测试音频设备: {device_name}")
                print(f"   - 设备名称: {selected_device.deviceName()}")
                print(f"   - 是否有效: {not selected_device.isNull()}")
                
                # 检查设备支持的格式
                formats = selected_device.supportedSampleRates()
                if formats:
                    print(f"   - 支持的采样率: {list(formats)[:5]}...")  # 只显示前5个
                
                self.result_label.setText(f"✅ 设备测试完成\n设备: {device_name}\n详细信息请查看控制台")
                self.result_label.setStyleSheet("""
                    QLabel {
                        background: #dcfce7;
                        border: 1px solid #16a34a;
                        border-radius: 6px;
                        padding: 15px;
                        font-size: 11pt;
                        color: #15803d;
                        min-height: 60px;
                    }
                """)
            else:
                print(f"🔊 使用系统默认音频设备")
                self.result_label.setText("ℹ️ 将使用系统默认音频设备")
                self.result_label.setStyleSheet("""
                    QLabel {
                        background: #dbeafe;
                        border: 1px solid #3b82f6;
                        border-radius: 6px;
                        padding: 15px;
                        font-size: 11pt;
                        color: #1d4ed8;
                        min-height: 60px;
                    }
                """)
                
        except Exception as e:
            print(f"测试音频设备时出错: {e}")
            self.result_label.setText(f"❌ 测试失败: {e}")
    
    def test_main_program(self):
        """测试主程序的音频功能"""
        try:
            from main_module import OBSControlApp
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            main_window = OBSControlApp()
            main_window.show()
            
            # 切换到音频播放器标签页
            tab_widget = main_window.tab_widget
            for i in range(tab_widget.count()):
                if "音频播放器" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
            
            self.result_label.setText("✅ 主程序已启动！\n请在音频播放器中测试设备切换功能")
            self.result_label.setStyleSheet("""
                QLabel {
                    background: #dbeafe;
                    border: 1px solid #3b82f6;
                    border-radius: 6px;
                    padding: 15px;
                    font-size: 11pt;
                    color: #1d4ed8;
                    min-height: 60px;
                }
            """)
            
            print("✅ 主程序已启动，请测试音频输出设备功能")
            
        except Exception as e:
            self.result_label.setText(f"❌ 启动主程序失败: {e}")
            print(f"启动主程序时出错: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AudioOutputTest()
    window.show()
    sys.exit(app.exec_())
