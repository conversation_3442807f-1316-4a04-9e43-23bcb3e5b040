#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
布局测试 - 验证碎片数据范围区域的完整显示
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGroupBox, QSpinBox, QFormLayout, QCheckBox
)
from PyQt5.QtCore import Qt

class LayoutTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🔧 布局测试 - 碎片数据范围完整显示')
        self.setGeometry(200, 200, 1000, 700)
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("⚡ 爆闪播放器 - 布局测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 说明文字
        desc = QLabel("🔧 测试碎片数据范围区域是否完整显示，所有输入框和文字都应该可见")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("color: #64748b; font-size: 12pt; padding: 10px;")
        main_layout.addWidget(desc)
        
        # 内容区域 - 只显示右侧面板进行测试
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # 左侧占位
        left_placeholder = QLabel("左侧面板\n(颜色管理\n轮播模式)")
        left_placeholder.setAlignment(Qt.AlignCenter)
        left_placeholder.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 2px dashed #cbd5e1;
                border-radius: 12px;
                padding: 50px;
                color: #64748b;
                font-size: 14pt;
            }
        """)
        content_layout.addWidget(left_placeholder, 1)
        
        # 右侧面板 - 重点测试区域
        right_panel = self.create_test_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        test_btn = QPushButton("✅ 布局测试通过")
        test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 150px;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(test_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
    def create_test_panel(self):
        """创建测试面板 - 使用与主程序相同的样式"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)  # 调整后的边距
        layout.setSpacing(10)  # 调整后的间距
        
        # 无视间隔设置
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)
        
        # 复选框
        checkbox = QCheckBox("自由无视间隔剪切")
        checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                font-size: 12pt;
            }
        """)
        interval_layout.addWidget(checkbox)
        
        # 碎片数据范围标题 - 调整后的样式
        fragment_title = QLabel("📊 碎片数据范围")
        fragment_title.setAlignment(Qt.AlignCenter)
        fragment_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #dc2626;
                font-size: 12pt;
                margin: 5px 0px;
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                padding: 6px;
                border-radius: 6px;
                border: 2px solid #dc2626;
            }
        """)
        interval_layout.addWidget(fragment_title)
        
        # 碎片设置容器 - 调整后的样式
        fragment_container = QWidget()
        fragment_container.setStyleSheet("""
            QWidget {
                background: linear-gradient(135deg, #fef9c3 0%, #fef08a 100%);
                border: 3px solid #eab308;
                border-radius: 12px;
                padding: 15px;
                margin: 2px;
            }
        """)
        fragment_layout = QFormLayout(fragment_container)
        fragment_layout.setSpacing(8)
        fragment_layout.setContentsMargins(10, 10, 10, 10)
        
        # 最小值 - 调整后的样式
        min_label = QLabel("最小:")
        min_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        min_spin = QSpinBox()
        min_spin.setValue(10)
        min_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(min_label, min_spin)
        
        # 最大值 - 调整后的样式
        max_label = QLabel("最大:")
        max_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        max_spin = QSpinBox()
        max_spin.setValue(30)
        max_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
        """)
        fragment_layout.addRow(max_label, max_spin)
        
        # 说明文字 - 调整后的样式
        fragment_info = QLabel("💡 启用后使用上述数值进行快速切换(ms)")
        fragment_info.setWordWrap(True)
        fragment_info.setStyleSheet("""
            QLabel {
                color: #92400e;
                font-size: 9pt;
                font-style: italic;
                margin-top: 5px;
                padding: 3px;
                background: rgba(255, 255, 255, 0.7);
                border-radius: 3px;
            }
        """)
        fragment_layout.addRow(fragment_info)
        
        interval_layout.addWidget(fragment_container)
        layout.addWidget(interval_group)
        
        # 添加其他区域占位
        other_group = QGroupBox("🎛️ 轮播设置【毫秒】")
        other_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        other_layout = QVBoxLayout(other_group)
        other_placeholder = QLabel("正常轮播间隔设置\n最小: 500ms\n最大: 2000ms")
        other_placeholder.setAlignment(Qt.AlignCenter)
        other_placeholder.setStyleSheet("color: #64748b; padding: 20px;")
        other_layout.addWidget(other_placeholder)
        
        layout.addWidget(other_group)
        
        return panel

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = LayoutTest()
    window.show()
    sys.exit(app.exec_())
