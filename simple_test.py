#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt

class SimpleTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('简单测试')
        self.setGeometry(300, 300, 400, 200)
        
        layout = QVBoxLayout()
        
        label = QLabel("爆闪播放器功能测试")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("测试按钮")
        button.clicked.connect(self.test_function)
        layout.addWidget(button)
        
        self.setLayout(layout)
        
    def test_function(self):
        print("测试功能正常工作！")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SimpleTest()
    window.show()
    sys.exit(app.exec_())
