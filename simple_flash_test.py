#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单爆闪播放器测试 - 验证修复后的启动功能
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_simple_flash():
    """测试简化版爆闪播放器"""
    try:
        print("🧪 开始测试简化版爆闪播放器...")
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        # 导入主程序
        from main_module import OBSControlApp
        
        print("✅ 主程序导入成功")
        
        # 创建主程序实例
        main_window = OBSControlApp()
        print("✅ 主程序实例创建成功")
        
        # 显示主窗口
        main_window.show()
        print("✅ 主窗口显示成功")
        
        # 切换到爆闪播放器标签页
        tab_widget = main_window.tab_widget
        for i in range(tab_widget.count()):
            if "爆闪播放器" in tab_widget.tabText(i):
                tab_widget.setCurrentIndex(i)
                print(f"✅ 已切换到爆闪播放器标签页 (索引: {i})")
                break
        else:
            print("❌ 未找到爆闪播放器标签页")
            return False
        
        # 添加一些测试颜色
        test_colors = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff"]
        main_window.flash_player_state["colors"] = test_colors
        print(f"✅ 已添加测试颜色: {test_colors}")
        
        # 更新颜色列表显示
        if hasattr(main_window, 'update_colors_list'):
            try:
                main_window.update_colors_list()
                print("✅ 颜色列表已更新")
            except:
                print("⚠️ 颜色列表更新失败，但不影响功能")
        
        print("\n🎉 测试完成！现在您可以:")
        print("   1. 在界面中查看碎片数据范围设置")
        print("   2. 勾选'自由无视间隔剪切'启用碎片模式")
        print("   3. 调整碎片数据范围的最小/最大值")
        print("   4. 点击'🚀 启动几何形状轮播'开始播放")
        print("   5. 播放窗口会全屏显示简单的几何形状")
        print("   6. 按ESC键或右键关闭播放窗口")
        print("\n💡 提示: 现在使用的是简化版播放窗口，避免了复杂的绘图导致的崩溃问题")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        if 'app' in locals():
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("测试错误")
            msg.setText(f"测试过程中发生错误: {e}")
            msg.setDetailedText(traceback.format_exc())
            msg.exec_()
        
        return False

if __name__ == '__main__':
    test_simple_flash()
